<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter, useRoute } from "vue-router";
import { message } from "@/utils/message";
import { verifyEmailRules } from "./utils/rule";
import { ref, reactive, toRaw, computed, onMounted } from "vue";
import { debounce } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";
import { useEventListener } from "@vueuse/core";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { bg, avatar, illustration } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";

import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import globalization from "@/assets/svg/globalization.svg?component";
import TypeIt from "@/components/ReTypeit/src";
import i18n, { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import { useLanguageStoreHook } from "@/store/modules/language";
import { useSettingStoreHook } from "@/store/modules/settings";
import { useTranslation } from "@/layout/hooks/useTranslation";
import {
  verifyEmail,
  resendVerification,
  type UserResult
} from "@/views/auth";
import AuthNavigation from "../components/AuthNavigation.vue";

defineOptions({
  name: "VerifyEmail"
});

const useLanguage = useLanguageStoreHook();
const useSetting = useSettingStoreHook();
const translation = useTranslation();

// Ensure settings are loaded if not already
if (Object.keys(useSetting.settings).length === 0) {
  useSetting.fetchPublicSettings();
}

const router = useRouter();
const route = useRoute();
const loading = ref(false);
const resendLoading = ref(false);
const disabled = ref(false);
const ruleFormRef = ref<FormInstance>();
const countdown = ref(0);
const countdownTimer = ref<NodeJS.Timeout | null>(null);

const { initStorage } = useLayout();
initStorage();

const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title, getDropdownItemStyle, getDropdownItemClass } = useNav();

const ruleForm = reactive({
  email: "",
  otp: ""
});

const languages = computed(() => {
  return useLanguage.languages?.map((language: any) => ({
    locale: language.code,
    native: language.nativeName
  }));
});

const currentLocale = computed(() => {
  return useLanguage.locale || i18n.global.locale.value;
});

const canResend = computed(() => {
  return countdown.value === 0 && !resendLoading.value;
});

onMounted(() => {
  // Get email from query params
  if (route.query.email) {
    ruleForm.email = route.query.email as string;
  }

  // Start countdown for resend
  startCountdown();
});

const startCountdown = () => {
  countdown.value = 60;
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!);
      countdownTimer.value = null;
    }
  }, 1000);
};

const onVerifyEmail = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    await formEl.validate();
    loading.value = true;
    const res: UserResult = await verifyEmail({
      email: ruleForm.email,
      code: ruleForm.otp
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    message(res.message, { type: "success" });
    // Redirect to login page
    await router.push("/login");
  } catch (error: any) {
    message(error?.response?.data?.message || error?.message, {
      type: "error"
    });
  } finally {
    loading.value = false;
  }
};

const onResendVerification = async () => {
  if (!canResend.value) return;

  try {
    resendLoading.value = true;
    const res: UserResult = await resendVerification({
      email: ruleForm.email
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    message(res.message, { type: "success" });
    startCountdown();
  } catch (error: any) {
    message(error?.response?.data?.message || error?.message, {
      type: "error"
    });
  } finally {
    resendLoading.value = false;
  }
};

const immediateDebounce: any = debounce(
  formRef => onVerifyEmail(formRef),
  1000,
  true
);

const handleSwitch = (locale: string) => {
  translation.switchLanguage(locale);
};

useEventListener(document, "keydown", ({ code }) => {
  if (
    ["Enter", "NumpadEnter"].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce(ruleFormRef.value);
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" alt="Bg" />
    <div class="flex-c absolute right-5 top-3">
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
      <el-dropdown trigger="click">
        <globalization
          class="hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"
        />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              v-for="lang in languages"
              :key="lang.locale"
              :style="getDropdownItemStyle(currentLocale, lang.locale)"
              :class="[
                'dark:!text-white',
                getDropdownItemClass(currentLocale, lang.locale)
              ]"
              @click="handleSwitch(lang.locale)"
            >
              {{ lang.native }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(illustration)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <h2 class="outline-none">
              <TypeIt
                :options="{
                  strings: [$t('Verify Email')],
                  cursor: false,
                  speed: 100
                }"
              />
            </h2>
          </Motion>

          <div class="text-center mb-4 text-gray-500">
            {{ $t("We've sent a verification code to") }}<br />
            <strong>{{ ruleForm.email }}</strong>
          </div>

          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="verifyEmailRules"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item prop="otp">
                <el-input
                  v-model="ruleForm.otp"
                  clearable
                  :placeholder="$t('OTP Code')"
                  :prefix-icon="useRenderIcon('ri:shield-keyhole-line')"
                  maxlength="6"
                  class="text-center"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-button
                class="w-full !uppercase"
                size="large"
                type="danger"
                round
                :loading="loading"
                :disabled="disabled"
                @click="onVerifyEmail(ruleFormRef)"
              >
                <IconifyIconOnline
                  :icon="'ri:shield-check-line'"
                  width="20"
                  class="mr-2"
                />
                {{ $t("Verify Email") }}
              </el-button>
            </Motion>

            <Motion :delay="200">
              <div class="text-center mt-4">
                <span class="text-gray-500">{{
                  $t("Didn't receive the code?")
                }}</span>
                <el-button
                  link
                  type="primary"
                  :disabled="!canResend"
                  :loading="resendLoading"
                  class="ml-1"
                  @click="onResendVerification"
                >
                  {{
                    canResend
                      ? $t("Resend Code")
                      : $t("Resend in {seconds}s", { seconds: countdown })
                  }}
                </el-button>
              </div>
            </Motion>

            <Motion :delay="250">
              <AuthNavigation
                :show-login="true"
                :show-register="false"
                :show-forgot-password="false"
                :custom-links="[
                  {
                    text: $t('Change Email'),
                    path: '/register'
                  }
                ]"
              />
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}

:deep(.text-center input) {
  text-align: center;
  font-size: 1.2em;
  letter-spacing: 0.5em;
  font-weight: bold;
}
</style>
