<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useRouter } from "vue-router";

// Props interface
interface Props {
  mainView?: string;
  selectedAgentId?: number | null;
  chatBot: any; // Chat bot instance từ parent
}

// Emits interface
interface Emits {
  (e: "selectAgent", agentUuid: string): void;
  (e: "showHistoryView", showCurrentAgentHistory?: boolean): void;
  (e: "createNewAgent"): void;
}

const props = withDefaults(defineProps<Props>(), {
  mainView: "list",
  selectedAgentId: null
});

const emit = defineEmits<Emits>();

const router = useRouter();

// Sử dụng chatBot từ props thay vì hook
const { searchQuery, filterType, selectedAgent, agents, filteredAgents } =
  props.chatBot;

const loading = ref(false);
const clientHeight = ref(400);
const chatSidebarContainer = ref(null);

const showHistoryView = (showCurrentAgentHistory = false) => {
  emit("showHistoryView", showCurrentAgentHistory);
};

const createNewAgent = () => {
  emit("createNewAgent");
};

const selectAgent = (agentUuid: string) => {
  emit("selectAgent", agentUuid);
};

const updateChatContainerHeight = () => {
  if (chatSidebarContainer.value) {
    const header = document.querySelector(".chat-header");
    const footer = document.querySelector(".chat-footer");
    const headerHeight = header ? header.getBoundingClientRect().height : 81;
    const footerHeight = footer ? footer.getBoundingClientRect().height : 125;
    const parentHeight = chatSidebarContainer.value.parentElement.clientHeight;
    const newHeight = parentHeight - headerHeight - footerHeight;
    clientHeight.value = newHeight > 0 ? newHeight : 400;
  }
};

onMounted(() => {
  // Gọi lần đầu khi component được mount
  nextTick(() => {
    updateChatContainerHeight();
  });
  window.addEventListener("resize", updateChatContainerHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateChatContainerHeight);
});
</script>

<template>
  <div
    ref="chatSidebarContainer"
    class="chat-sidebar-container w-full max-w-[360px] h-full relative"
  >
    <h1 class="text-2xl font-bold text-gray-800 mb-4">Các Agent</h1>
    <div class="mb-4">
      <el-input
        v-model="searchQuery"
        placeholder="Tìm kiếm Agent..."
        :prefix-icon="useRenderIcon('lucide:search')"
        clearable
      />
    </div>
    <div class="mb-4">
      <el-radio-group
        v-model="filterType"
        size="small"
        class="w-full flex gap-2 text-sm"
      >
        <el-radio-button value="all" class="flex-1 text-center">
          Tất cả
        </el-radio-button>
        <el-radio-button value="personal" class="flex-1 text-center">
          Cá nhân
        </el-radio-button>
        <el-radio-button value="team" class="flex-1 text-center">
          Team
        </el-radio-button>
      </el-radio-group>
    </div>
    <div class="agents-content">
      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center py-8">
        <el-icon class="is-loading text-2xl text-gray-400">
          <IconifyIconOffline :icon="useRenderIcon('ri:loader-4-line')" />
        </el-icon>
      </div>

      <!-- Agents List -->
      <div
        v-else-if="filteredAgents.length > 0"
        class="agent-list"
        :style="{ height: clientHeight + 'px', maxHeight: clientHeight + 'px' }"
      >
        <div v-for="agent in filteredAgents" :key="agent.uuid">
          <el-popover
            placement="right-start"
            :width="350"
            trigger="hover"
            :show-after="500"
            popper-class="!p-0"
          >
            <template #reference>
              <div
                class="agent-item flex items-center p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-100 mb-2 w-full"
                :class="{
                  selected: selectedAgent && selectedAgent.uuid === agent.uuid
                }"
                @click="selectAgent(agent.uuid)"
              >
                <img
                  :src="agent.logo"
                  alt="logo"
                  class="w-10 h-10 rounded-full mr-3 object-cover flex-shrink-0"
                />
                <div class="flex-grow overflow-hidden">
                  <h3 class="font-semibold text-gray-800 truncate text-sm">
                    {{ agent.name }}
                  </h3>
                  <p class="text-sm text-gray-500 truncate">
                    {{ agent.description }}
                  </p>
                </div>
              </div>
            </template>
            <div class="p-4">
              <div class="flex items-center mb-4">
                <img
                  :src="agent.logo"
                  class="w-12 h-12 rounded-full mr-4 object-cover flex-shrink-0"
                  alt="logo"
                />
                <div class="overflow-hidden">
                  <h4 class="font-bold text-lg text-gray-800 truncate">
                    {{ agent.name }}
                  </h4>
                  <el-tag
                    size="small"
                    :type="agent.botType === 'personal' ? 'info' : 'success'"
                  >
                    {{ agent.botType === "personal" ? "Cá nhân" : "Team" }}
                  </el-tag>
                </div>
              </div>
              <p class="text-sm text-gray-600 mb-4">
                {{ agent.description }}
              </p>
              <div
                class="bg-slate-50 p-3 rounded-lg border border-slate-200 text-xs"
              >
                <div class="flex justify-between mb-2">
                  <span class="text-gray-500">Mô hình AI:</span>
                  <span class="font-medium text-gray-700">
                    {{ agent.aiModel?.name }}
                  </span>
                </div>
                <div class="flex justify-between mb-2">
                  <span class="text-gray-500"> Phiên bản: </span>
                  <span class="font-medium text-gray-700">
                    {{ agent.version || "1.0.0" }}
                  </span>
                </div>

                <div class="flex justify-between">
                  <span class="text-gray-500">Tác giả:</span>
                  <span class="font-medium text-gray-700">
                    {{ agent.owner?.name || agent.owner?.fullName }}
                  </span>
                </div>
              </div>
              <div
                v-if="agent.metadata?.tags && agent.metadata?.tags?.length > 0"
                class="mt-3"
              >
                <h5 class="text-xs font-semibold text-gray-500 mb-2">Thẻ:</h5>
                <div class="flex flex-wrap gap-2">
                  <el-tag
                    v-for="tag in agent.metadata.tags"
                    :key="tag"
                    size="small"
                    effect="plain"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-popover>
        </div>
      </div>

      <!-- Empty State -->
      <div
        v-else
        class="flex flex-col items-center justify-center py-8 text-center"
      >
        <IconifyIconOffline
          :icon="useRenderIcon('ri:robot-line')"
          class="text-4xl text-gray-300 mb-4"
        />
        <h3 class="text-lg font-medium text-gray-500 mb-4">
          {{ searchQuery ? "Không tìm thấy agent" : "Chưa có agent nào" }}
        </h3>
        <p class="text-sm text-gray-400 !mb-4">
          {{
            searchQuery
              ? "Thử tìm kiếm với từ khóa khác"
              : "Tạo agent đầu tiên để bắt đầu trò chuyện"
          }}
        </p>
        <el-button
          v-if="!searchQuery"
          type="primary"
          size="small"
          round
          :icon="useRenderIcon('ri:add-line')"
          @click="createNewAgent"
        >
          Tạo Agent Mới
        </el-button>
      </div>
    </div>
    <div class="absolute bottom-0 left-0 right-0 flex justify-center">
      <el-button type="danger" class="w-full !rounded-none">
        <IconifyIconOnline :icon="'mdi:history'" />
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.chat-sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background: white;
  border-right: 1px solid #e5e7eb;
  padding: 1rem;
}

.agents-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0; // Important for flex child to be scrollable
  padding-right: 0.5rem;
  margin-bottom: 1rem;
}

.buttons-section {
  display: flex;
  gap: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
  flex-shrink: 0; // Prevent buttons from shrinking
}

.agent-item.selected,
.history-agent-item.selected {
  background-color: #eff6ff;
}

.agents-content {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 3px;
    transition: background-color 0.3s ease;
  }

  &:hover::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #94a3b8;
  }

  // Auto-hide scrollbar for Firefox
  &:hover {
    scrollbar-color: #cbd5e1 transparent;
  }
}

:deep(.el-radio-group) {
  width: 100%;
  display: flex;

  .el-radio-button {
    flex: 1;

    .el-radio-button__inner {
      width: 100%;
      text-align: center;
      border-radius: 6px;
      font-size: 12px;
      padding: 8px 12px;
    }
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
  }
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

:deep(.el-tag) {
  border-radius: 4px;
  font-size: 11px;
  padding: 2px 6px;
}

.bg-slate-50 {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
}

// Loading animation
:deep(.el-icon.is-loading) {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Fade in animation for agents
.agent-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Empty state animation
.text-4xl {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// Responsive adjustments
@media (max-height: 600px) {
  .chat-sidebar-container {
    height: 100vh;
    max-height: 100vh;
  }

  .agents-content {
    min-height: 200px;
  }
}

@media (max-width: 768px) {
  .chat-sidebar-container {
    padding: 0.75rem;
  }

  .buttons-section {
    flex-direction: column;
    gap: 0.25rem;
  }
}
</style>
