import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

export const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: () => $t("Please enter knowledge base name"),
      trigger: "blur"
    }
  ],
  type: [
    {
      required: true,
      message: () => $t("Please select knowledge base type"),
      trigger: "change"
    }
  ],
  status: [
    {
      required: true,
      message: () => $t("Please select status"),
      trigger: "change"
    }
  ],
  ownerType: [
    {
      required: true,
      message: () => $t("Please enter owner type"),
      trigger: "blur"
    }
  ],
  ownerId: [
    {
      required: true,
      message: () => $t("Please enter owner ID"),
      trigger: "blur"
    }
  ],
  content: [
    {
      validator: (rule, value, callback) => {
        // Content is required for text type
        const form = rule.field?.split('.')[0];
        if (form && form.type === 'text' && !value) {
          callback(new Error($t("Please enter content for text type")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});
