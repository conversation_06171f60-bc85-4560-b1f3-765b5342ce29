<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { PlusDialogForm } from "plus-pro-components";
import { dropdownLanguages } from "@/views/language/utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";

interface Props {
  visible: boolean;
  values: FieldValues;
  groups: any[];
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "submit", values: FieldValues): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  values: () => ({}),
  groups: () => []
});

const emit = defineEmits<Emits>();

const loading = ref(false);
const formRef = ref();
const languages = ref([]);

onMounted(async () => {
  try {
    const { data } = await dropdownLanguages();
    languages.value = useConvertKeyToCamel(data);
  } catch (error) {
    console.error("Failed to load languages:", error);
  }
});

const groupOptions = computed(() => {
  return props.groups.map(group => ({
    label: group.name || group,
    value: group.name || group
  }));
});

const languageOptions = computed(() => {
  return languages.value.map((lang: any) => ({
    label: `${lang.name} (${lang.code})`,
    value: lang.code
  }));
});

const columns = [
  {
    label: computed(() => $t("Translation Key")),
    prop: "key",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input translation key"),
        trigger: ["blur"]
      },
      {
        min: 2,
        max: 255,
        message: $t("Length must be between 2 and 255 characters"),
        trigger: ["blur"]
      },
      {
        pattern: /^[a-zA-Z0-9._-]+$/,
        message: $t("Key can only contain letters, numbers, dots, underscores and hyphens"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: $t("e.g., user.name, common.save")
    }
  },
  {
    label: computed(() => $t("Language")),
    prop: "languageCode",
    valueType: "select",
    required: true,
    options: languageOptions,
    rules: [
      {
        required: true,
        message: $t("Please select language"),
        trigger: ["change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Select language"),
      filterable: true
    }
  },
  {
    label: computed(() => $t("Translation Value")),
    prop: "value",
    valueType: "textarea",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input translation value"),
        trigger: ["blur"]
      },
      {
        max: 5000,
        message: $t("Translation value cannot exceed 5000 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: $t("Enter translation value"),
      showWordLimit: true,
      autosize: { minRows: 3, maxRows: 8 }
    }
  },
  {
    label: computed(() => $t("Group")),
    prop: "group",
    valueType: "select",
    options: groupOptions,
    fieldProps: {
      placeholder: $t("Select or enter group"),
      filterable: true,
      allowCreate: true
    }
  },
  {
    label: computed(() => $t("Description")),
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Enter description (optional)"),
      showWordLimit: true,
      autosize: { minRows: 2, maxRows: 4 }
    }
  },
  {
    label: computed(() => $t("Is Plural")),
    prop: "isPlural",
    valueType: "switch",
    fieldProps: {
      activeText: $t("Yes"),
      inactiveText: $t("No")
    }
  },
  {
    label: computed(() => $t("Is Active")),
    prop: "isActive",
    valueType: "switch",
    fieldProps: {
      activeText: $t("Active"),
      inactiveText: $t("Inactive")
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    required: true,
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ],
    fieldProps: {
      placeholder: $t("Select status")
    }
  }
];

const handleSubmit = async (values: FieldValues) => {
  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDialogForm
    ref="formRef"
    v-model:visible="visible"
    v-model="values"
    :form="{
      columns,
      labelWidth: '140px',
      labelPosition: 'right',
      hasFooter: true
    }"
    :dialog="{
      title: values.id ? $t('Edit Translation') : $t('Add Translation'),
      width: '700px',
      top: '5vh',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      draggable: true
    }"
    @confirm="handleSubmit"
    @update:visible="emit('update:visible', $event)"
  />
</template>
