// Mock data for development and testing
import dayjs from "dayjs";

// Mock Users
export const mockUsers = [
  {
    id: 1,
    uuid: "user-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://i.pravatar.cc/150?img=1",
    status: "active",
    createdAt: dayjs().subtract(30, "days").toISOString(),
    updatedAt: dayjs().subtract(1, "day").toISOString()
  },
  {
    id: 2,
    uuid: "user-002", 
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://i.pravatar.cc/150?img=2",
    status: "active",
    createdAt: dayjs().subtract(25, "days").toISOString(),
    updatedAt: dayjs().subtract(2, "hours").toISOString()
  },
  {
    id: 3,
    uuid: "user-003",
    name: "<PERSON>",
    email: "<EMAIL>", 
    avatar: "https://i.pravatar.cc/150?img=3",
    status: "inactive",
    createdAt: dayjs().subtract(20, "days").toISOString(),
    updatedAt: dayjs().subtract(5, "days").toISOString()
  }
];

// Mock AI Models
export const mockAiModels = [
  {
    id: 1,
    name: "GPT-4",
    provider: "OpenAI",
    status: "active",
    createdAt: dayjs().subtract(60, "days").toISOString()
  },
  {
    id: 2,
    name: "Claude-3",
    provider: "Anthropic", 
    status: "active",
    createdAt: dayjs().subtract(45, "days").toISOString()
  },
  {
    id: 3,
    name: "Gemini Pro",
    provider: "Google",
    status: "active",
    createdAt: dayjs().subtract(30, "days").toISOString()
  }
];

// Mock Bots
export const mockBots = [
  {
    id: 1,
    uuid: "bot-001",
    name: "Customer Support Bot",
    logo: "https://i.pravatar.cc/150?img=10",
    description: "AI assistant for customer support inquiries",
    aiModelId: 1,
    aiModel: mockAiModels[0],
    systemPrompt: "You are a helpful customer support assistant. Be polite and professional.",
    greetingMessage: "Hello! How can I help you today?",
    starterMessages: ["How can I track my order?", "I need help with returns", "What are your business hours?"],
    toolCallingMode: "auto",
    visibility: "public",
    botType: "organization",
    status: "active",
    createdAt: dayjs().subtract(15, "days").toISOString(),
    updatedAt: dayjs().subtract(1, "hour").toISOString(),
    // Conversation có sẵn cho bot này
    conversation: {
      id: 1,
      title: "Customer Support Session",
      botUuid: "bot-001",
      status: "active",
      messageCount: 5,
      lastMessageAt: dayjs().subtract(10, "minutes").toISOString(),
      createdAt: dayjs().subtract(2, "hours").toISOString(),
      updatedAt: dayjs().subtract(10, "minutes").toISOString()
    }
  },
  {
    id: 2,
    uuid: "bot-002",
    name: "Code Assistant",
    logo: "https://i.pravatar.cc/150?img=11",
    description: "AI assistant for coding help and debugging",
    aiModelId: 2,
    aiModel: mockAiModels[1],
    systemPrompt: "You are an expert programming assistant. Help with code and technical questions.",
    greetingMessage: "Hi! I'm here to help with your coding questions.",
    starterMessages: ["Debug my code", "Explain this algorithm", "Best practices for React"],
    toolCallingMode: "required",
    visibility: "private",
    botType: "personal",
    status: "active",
    createdAt: dayjs().subtract(10, "days").toISOString(),
    updatedAt: dayjs().subtract(30, "minutes").toISOString(),
    // Conversation có sẵn cho bot này
    conversation: {
      id: 2,
      title: "Debugging Session",
      botUuid: "bot-002",
      status: "active",
      messageCount: 8,
      lastMessageAt: dayjs().subtract(1, "hour").toISOString(),
      createdAt: dayjs().subtract(1, "day").toISOString(),
      updatedAt: dayjs().subtract(1, "hour").toISOString()
    }
  },
  {
    id: 3,
    uuid: "bot-003",
    name: "Writing Assistant",
    logo: "https://i.pravatar.cc/150?img=12",
    description: "AI assistant for writing and content creation",
    aiModelId: 3,
    aiModel: mockAiModels[2],
    systemPrompt: "You are a creative writing assistant. Help with writing, editing, and content creation.",
    greetingMessage: "Welcome! Let's create something amazing together.",
    starterMessages: ["Help me write an essay", "Improve this paragraph", "Creative writing tips"],
    toolCallingMode: "none",
    visibility: "public",
    botType: "organization",
    status: "draft",
    createdAt: dayjs().subtract(5, "days").toISOString(),
    updatedAt: dayjs().subtract(2, "hours").toISOString()
    // Không có conversation cho bot này - sẽ tạo mới khi cần
  }
];

// Mock Chats
export const mockChats = [
  {
    id: 1,
    uuid: "chat-001",
    title: "Customer Support Session",
    userId: 1,
    user: mockUsers[0],
    botId: 1,
    bot: mockBots[0],
    status: "active",
    messageCount: 15,
    lastMessageAt: dayjs().subtract(10, "minutes").toISOString(),
    lastMessage: {
      id: 45,
      content: "Thank you for your help! That solved my issue.",
      role: "user",
      createdAt: dayjs().subtract(10, "minutes").toISOString()
    },
    createdAt: dayjs().subtract(2, "hours").toISOString(),
    updatedAt: dayjs().subtract(10, "minutes").toISOString()
  },
  {
    id: 2,
    uuid: "chat-002", 
    title: "Debugging Session",
    userId: 2,
    user: mockUsers[1],
    botId: 2,
    bot: mockBots[1],
    status: "active",
    messageCount: 8,
    lastMessageAt: dayjs().subtract(1, "hour").toISOString(),
    lastMessage: {
      id: 32,
      content: "Here's the corrected code with proper error handling:",
      role: "assistant",
      createdAt: dayjs().subtract(1, "hour").toISOString()
    },
    createdAt: dayjs().subtract(1, "day").toISOString(),
    updatedAt: dayjs().subtract(1, "hour").toISOString()
  },
  {
    id: 3,
    uuid: "chat-003",
    title: "Content Creation Help",
    userId: 3,
    user: mockUsers[2],
    botId: 3,
    bot: mockBots[2],
    status: "archived",
    messageCount: 22,
    lastMessageAt: dayjs().subtract(3, "days").toISOString(),
    lastMessage: {
      id: 67,
      content: "Perfect! The blog post looks great now.",
      role: "user",
      createdAt: dayjs().subtract(3, "days").toISOString()
    },
    createdAt: dayjs().subtract(5, "days").toISOString(),
    updatedAt: dayjs().subtract(3, "days").toISOString()
  }
];

// Mock Messages
export const mockMessages = [
  {
    id: 1,
    uuid: "msg-001",
    chatId: 1,
    chat: mockChats[0],
    role: "user",
    content: "Hi, I'm having trouble with my account login",
    contentType: "text",
    isEdited: false,
    createdAt: dayjs().subtract(2, "hours").toISOString(),
    updatedAt: dayjs().subtract(2, "hours").toISOString()
  },
  {
    id: 2,
    uuid: "msg-002",
    chatId: 1,
    chat: mockChats[0],
    role: "assistant",
    content: "I'd be happy to help you with your login issue. Can you tell me what specific error you're seeing?",
    contentType: "text",
    isEdited: false,
    createdAt: dayjs().subtract(2, "hours").add(1, "minute").toISOString(),
    updatedAt: dayjs().subtract(2, "hours").add(1, "minute").toISOString()
  },
  {
    id: 3,
    uuid: "msg-003",
    chatId: 2,
    chat: mockChats[1],
    role: "user",
    content: "Can you help me debug this JavaScript function?",
    contentType: "text",
    isEdited: false,
    createdAt: dayjs().subtract(1, "day").toISOString(),
    updatedAt: dayjs().subtract(1, "day").toISOString()
  }
];

// Mock Dashboard Stats
export const mockDashboardStats = {
  totalBots: 25,
  totalChats: 1250,
  totalMessages: 15680,
  totalUsers: 342,
  activeBots: 18,
  activeChats: 89,
  todayMessages: 456,
  onlineUsers: 23,
  trends: {
    bots: { value: 12.5, isUp: true },
    chats: { value: 8.3, isUp: true },
    messages: { value: 15.7, isUp: true },
    users: { value: 5.2, isUp: false }
  }
};

// Mock Recent Activities
export const mockRecentActivities = [
  {
    id: 1,
    type: "bot.created",
    description: "created a new bot 'Sales Assistant'",
    user: mockUsers[0],
    createdAt: dayjs().subtract(30, "minutes").toISOString(),
    metadata: { botId: 4 }
  },
  {
    id: 2,
    type: "chat.created",
    description: "started a new chat session",
    user: mockUsers[1],
    createdAt: dayjs().subtract(1, "hour").toISOString(),
    metadata: { chatId: 4 }
  },
  {
    id: 3,
    type: "message.sent",
    description: "sent a message in customer support chat",
    user: mockUsers[2],
    createdAt: dayjs().subtract(2, "hours").toISOString(),
    metadata: { messageId: 89, chatId: 1 }
  },
  {
    id: 4,
    type: "user.login",
    description: "logged into the system",
    user: mockUsers[0],
    createdAt: dayjs().subtract(3, "hours").toISOString(),
    metadata: {}
  },
  {
    id: 5,
    type: "bot.updated",
    description: "updated bot configuration for 'Code Assistant'",
    user: mockUsers[1],
    createdAt: dayjs().subtract(4, "hours").toISOString(),
    metadata: { botId: 2 }
  }
];

// Helper functions for pagination
export const paginate = <T>(data: T[], page: number = 1, perPage: number = 10) => {
  const offset = (page - 1) * perPage;
  const paginatedData = data.slice(offset, offset + perPage);
  
  return {
    data: paginatedData,
    total: data.length,
    currentPage: page,
    perPage: perPage,
    lastPage: Math.ceil(data.length / perPage),
    from: offset + 1,
    to: offset + paginatedData.length
  };
};

// Helper function to simulate API delay
export const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));
