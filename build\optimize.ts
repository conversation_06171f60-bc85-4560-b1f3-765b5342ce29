/**
 * This file is used for the `optimizeDeps.include` dependency pre-build configuration in `vite.config.ts`
 * Dependency pre-build: when `vite` starts, it will compile the modules in the include list below into esm format and cache them to the node_modules/.vite folder. When the page loads the corresponding module, if the browser has cache, it will read the browser cache, if not, it will read the local cache and load on demand
 * Especially when you disable browser cache (this should only happen during debugging), you must add the corresponding module to include, otherwise you will encounter page switching lag in development environment (vite will think it is a new dependency package and will reload and force refresh the page), because it can neither use browser cache nor cache in local node_modules/.vite
 * Tip: If the third-party library you use is globally imported, that is, imported into the src/main.ts file, you don't need to add it to include, because vite will automatically cache them to node_modules/.vite
 */
const include = [
  "qs",
  "mitt",
  "dayjs",
  "axios",
  "pinia",
  "vue-types",
  "js-cookie",
  "vue-tippy",
  "pinyin-pro",
  "sortablejs",
  "@vueuse/core",
  "@pureadmin/utils",
  "responsive-storage"
];

/**
 * Dependencies forcibly excluded from pre-build
 * Tip: The platform's recommended usage is to import where needed and all are individual imports, no pre-build needed, just let the browser load directly
 */
const exclude = ["@iconify/json"];

export { include, exclude };
