import { $t } from "@/plugins/i18n";
import { useSettingStoreHook } from "@/store/modules/settings";
import { EMAIL_REGEX, USERNAME_REGEX, checkPasswordStrength } from "./common";

// Base validator function type
type ValidatorFunction = (rule: any, value: any, callback: Function) => void;

// Email validator
export const createEmailValidator = (): ValidatorFunction => {
  return (_rule: any, value: string, callback: Function) => {
    if (!value) {
      callback(new Error($t("Email is required")));
      return;
    }

    if (!EMAIL_REGEX.test(value)) {
      callback(new Error($t("Please enter a valid email address")));
      return;
    }

    callback();
  };
};

// Username validator
export const createUsernameValidator = (
  options: {
    minLength?: number;
    maxLength?: number;
    required?: boolean;
  } = {}
): ValidatorFunction => {
  const { minLength = 3, maxLength = 20, required = true } = options;

  return (_rule: any, value: string, callback: Function) => {
    if (!value) {
      if (required) {
        callback(new Error($t("Username is required")));
      } else {
        callback();
      }
      return;
    }

    if (value.length < minLength) {
      callback(
        new Error(
          $t("Username must be at least :length characters", {
            length: minLength
          })
        )
      );
      return;
    }

    if (value.length > maxLength) {
      callback(
        new Error(
          $t("Username must be less than :length characters", {
            length: maxLength
          })
        )
      );
      return;
    }

    if (!USERNAME_REGEX.test(value)) {
      callback(
        new Error(
          $t("Username can only contain letters, numbers and underscores")
        )
      );
      return;
    }

    callback();
  };
};

// Password validator with dynamic settings
export const createPasswordValidator = (
  options: {
    useSettings?: boolean;
    minLength?: number;
    requireNumbers?: boolean;
    requireSymbols?: boolean;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
  } = {}
): ValidatorFunction => {
  return (_rule: any, value: string, callback: Function) => {
    if (!value) {
      callback(new Error($t("Password is required")));
      return;
    }

    let requirements = options;

    // Use dynamic settings if enabled
    if (options.useSettings !== false) {
      const security = useSettingStoreHook()?.settings?.security;
      requirements = {
        minLength: security?.passwordMinLength || options.minLength || 6,
        requireNumbers:
          security?.passwordRequireNumbers ?? options.requireNumbers ?? false,
        requireSymbols:
          security?.passwordRequireSymbols ?? options.requireSymbols ?? false,
        requireUppercase:
          security?.passwordRequireUppercase ??
          options.requireUppercase ??
          false,
        requireLowercase:
          security?.passwordRequireLowercases ??
          options.requireLowercase ??
          false
      };
    }

    const strength = checkPasswordStrength(value, requirements);

    if (!strength.isValid) {
      callback(new Error(strength.feedback[0]));
      return;
    }

    callback();
  };
};

// Confirm password validator
export const createConfirmPasswordValidator = (
  passwordField: string
): ValidatorFunction => {
  return (rule: any, value: string, callback: Function) => {
    if (!value) {
      callback(new Error($t("Please confirm your password")));
      return;
    }

    // Get the form instance from the rule context
    const form = rule.form || rule.model;
    if (form && form[passwordField] !== value) {
      callback(new Error($t("Passwords do not match")));
      return;
    }

    callback();
  };
};

// OTP validator
export const createOtpValidator = (
  options: {
    length?: number;
    required?: boolean;
  } = {}
): ValidatorFunction => {
  const { length = 6, required = true } = options;

  return (_rule: any, value: string, callback: Function) => {
    if (!value) {
      if (required) {
        callback(new Error($t("Verification code is required")));
      } else {
        callback();
      }
      return;
    }

    if (value.length !== length) {
      callback(
        new Error($t("Verification code must be :length digits", { length }))
      );
      return;
    }

    if (!/^\d+$/.test(value)) {
      callback(new Error($t("Verification code must contain only numbers")));
      return;
    }

    callback();
  };
};

// Name validator
export const createNameValidator = (
  options: {
    minLength?: number;
    maxLength?: number;
    required?: boolean;
    field?: string;
  } = {}
): ValidatorFunction => {
  const {
    minLength = 1,
    maxLength = 50,
    required = true,
    field = "Name"
  } = options;

  return (_rule: any, value: string, callback: Function) => {
    if (!value || value.trim() === "") {
      if (required) {
        callback(new Error($t(":field is required", { field })));
      } else {
        callback();
      }
      return;
    }

    const trimmedValue = value.trim();

    if (trimmedValue.length < minLength) {
      callback(
        new Error(
          $t(":field must be at least :length characters", {
            field,
            length: minLength
          })
        )
      );
      return;
    }

    if (trimmedValue.length > maxLength) {
      callback(
        new Error(
          $t(":field must be less than :length characters", {
            field,
            length: maxLength
          })
        )
      );
      return;
    }

    // Check for valid name characters (letters, spaces, hyphens, apostrophes)
    if (!/^[\p{L}\s]+$/u.test(trimmedValue)) {
      callback(new Error($t(":field contains invalid characters", { field })));
      return;
    }

    callback();
  };
};

// Terms acceptance validator
export const createTermsValidator = (): ValidatorFunction => {
  return (_rule: any, value: boolean, callback: Function) => {
    if (!value) {
      callback(new Error($t("Please accept the terms and conditions")));
      return;
    }

    callback();
  };
};

// Phone number validator
export const createPhoneValidator = (
  options: {
    required?: boolean;
  } = {}
): ValidatorFunction => {
  const { required = false } = options;

  return (_rule: any, value: string, callback: Function) => {
    if (!value) {
      if (required) {
        callback(new Error($t("Phone number is required")));
      } else {
        callback();
      }
      return;
    }

    // Remove all non-digit characters for validation
    const cleanPhone = value.replace(/\D/g, "");

    if (cleanPhone.length < 10 || cleanPhone.length > 15) {
      callback(new Error($t("Please enter a valid phone number")));
      return;
    }

    callback();
  };
};

// Generic required validator
export const createRequiredValidator = (field: string): ValidatorFunction => {
  return (_rule: any, value: any, callback: Function) => {
    if (!value || (typeof value === "string" && value.trim() === "")) {
      callback(new Error($t(":field is required", { field })));
      return;
    }

    callback();
  };
};
