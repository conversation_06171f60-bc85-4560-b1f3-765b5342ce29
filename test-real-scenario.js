// Test real scenario with login response
console.log("=== Real Scenario Test ===");

// Scenario 1: Login response with expires_in = 3600 (1 hour)
const realLoginResponse = {
  "success": true,
  "message": "Login successful.",
  "data": {
    "user": {
      "username": "admin",
      "first_name": "<PERSON><PERSON><PERSON>",
      "last_name": "A",
      "full_name": "Nguyen Van A",
      "avatar": null,
      "permissions": ["*:*:*"],
      "roles": ["admin"]
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.real_token",
    "token_type": "Bearer",
    "expires_in": 3600 // 1 hour
  }
};

// Convert to camelCase
function convertSnakeToCamel(obj) {
  if (!obj) return obj;
  const convertKey = (key) => key.replace(/_([a-z])/g, (_, char) => char.toUpperCase());
  const convertObjectKeys = (o) => {
    if (Array.isArray(o)) {
      return o.map(item => convertObjectKeys(item));
    } else if (o !== null && o !== undefined && o.constructor === Object) {
      return Object.keys(o).reduce((acc, key) => {
        const camelKey = convertKey(key);
        acc[camelKey] = convertObjectKeys(o[key]);
        return acc;
      }, {});
    }
    return o;
  };
  return convertObjectKeys(obj);
}

const convertedData = convertSnakeToCamel(realLoginResponse.data);

console.log("=== Login Response Analysis ===");
console.log("Original response:", JSON.stringify(realLoginResponse.data, null, 2));
console.log("\nConverted to camelCase:", JSON.stringify(convertedData, null, 2));

// Simulate setToken
const now = Date.now();
const tokenData = {
  accessToken: convertedData.token,
  refreshToken: convertedData.refreshToken || convertedData.token, // Use same if no separate refresh token
  expires: now + (convertedData.expiresIn * 1000)
};

console.log("\n=== Token Data ===");
console.log("Access Token:", tokenData.accessToken);
console.log("Refresh Token:", tokenData.refreshToken);
console.log("Same token used for refresh:", tokenData.accessToken === tokenData.refreshToken);
console.log("Expires timestamp:", tokenData.expires);
console.log("Expires date:", new Date(tokenData.expires).toISOString());
console.log("Valid for (hours):", (tokenData.expires - now) / (1000 * 60 * 60));

// Cookie calculation
const cookieExpires = Math.max((tokenData.expires - Date.now()) / 86400000, 0.001);
console.log("\n=== Cookie Storage ===");
console.log("Cookie expires (days):", cookieExpires);
console.log("Cookie expires (hours):", cookieExpires * 24);

// Test token check logic
function checkToken(tokenData, currentTime = Date.now()) {
  const timeRemaining = tokenData.expires - currentTime;
  const expired = timeRemaining <= 0;
  
  return {
    expired,
    timeRemaining,
    timeRemainingMinutes: timeRemaining / (1000 * 60),
    timeRemainingHours: timeRemaining / (1000 * 60 * 60)
  };
}

console.log("\n=== Token Status Checks ===");

// Check immediately
const immediateCheck = checkToken(tokenData);
console.log("Immediate check:");
console.log("- Expired:", immediateCheck.expired);
console.log("- Time remaining (hours):", immediateCheck.timeRemainingHours.toFixed(2));

// Check after 30 minutes
const after30min = checkToken(tokenData, now + (30 * 60 * 1000));
console.log("\nAfter 30 minutes:");
console.log("- Expired:", after30min.expired);
console.log("- Time remaining (minutes):", after30min.timeRemainingMinutes.toFixed(2));

// Check after 1 hour (should be expired)
const after1hour = checkToken(tokenData, now + (60 * 60 * 1000));
console.log("\nAfter 1 hour:");
console.log("- Expired:", after1hour.expired);
console.log("- Time remaining (minutes):", after1hour.timeRemainingMinutes.toFixed(2));

// Check after 1 hour 5 minutes (definitely expired)
const after65min = checkToken(tokenData, now + (65 * 60 * 1000));
console.log("\nAfter 1 hour 5 minutes:");
console.log("- Expired:", after65min.expired);
console.log("- Time remaining (minutes):", after65min.timeRemainingMinutes.toFixed(2));

console.log("\n=== Refresh Token Scenarios ===");

// Scenario A: Refresh token success
console.log("\nScenario A: Refresh token success");
const refreshSuccessResponse = {
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzUxMiJ9.newAdmin",
    "refreshToken": "eyJhbGciOiJIUzUxMiJ9.newAdminRefresh",
    "expires": "2030/10/30 23:59:59"
  }
};

const newTokenData = {
  accessToken: refreshSuccessResponse.data.accessToken,
  refreshToken: refreshSuccessResponse.data.refreshToken,
  expires: new Date(refreshSuccessResponse.data.expires).getTime()
};

const newTokenCheck = checkToken(newTokenData);
console.log("New token status:");
console.log("- Expired:", newTokenCheck.expired);
console.log("- Valid for (years):", newTokenCheck.timeRemainingHours / (24 * 365));

// Scenario B: Refresh token failure
console.log("\nScenario B: Refresh token failure");
console.log("- API returns 401 or error");
console.log("- User should be logged out");
console.log("- Redirect to login page");

console.log("\n=== Recommendations ===");
console.log("1. ✅ Current logic handles token expiry correctly");
console.log("2. ✅ Refresh token mechanism is in place");
console.log("3. ✅ Error handling for refresh failures");
console.log("4. ⚠️  Same token used for access and refresh (may cause issues)");
console.log("5. 💡 Consider refreshing token before expiry (e.g., 5 minutes before)");
console.log("6. 💡 Add retry mechanism for failed refresh attempts");
console.log("7. 💡 Add user notification for token refresh events");

console.log("\n=== Test Complete ===");
