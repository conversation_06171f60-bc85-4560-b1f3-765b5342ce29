import { useNav } from "./useNav";
import { useRoute } from "vue-router";
import { watch, onBeforeMount, type Ref, type WritableComputedRef } from "vue";
import { useLanguageStore } from "@/store/modules/language";
import i18n from "@/plugins/i18n";
import { storageLocal } from "@pureadmin/utils";

export function useTranslation(ref?: Ref) {
  const { changeTitle, handleResize } = useNav();
  const { locale } = i18n.global;
  const route = useRoute();
  const languageStore = useLanguageStore();
  const localStorage = storageLocal();

  async function switchLanguage(localeCode: string) {
    // Update localStorage first
    localStorage.setItem("locale", localeCode);

    // Update store
    languageStore.setLocale(localeCode);

    // Update i18n locale (both local and global)
    (locale as WritableComputedRef<string>).value = localeCode;
    i18n.global.locale.value = localeCode;

    // Load translations if needed
    await languageStore.loadTranslations(localeCode);

    // Handle resize if ref provided
    ref && handleResize(ref.value);

    // Update title
    changeTitle(route.meta);
  }

  watch(
    () => [route?.path, locale.value],
    () => {
      if (route?.meta?.title) {
        changeTitle(route.meta);
      }
    },
    {
      immediate: true
    }
  );

  onBeforeMount(() => {
    // Sync locale from localStorage on mount
    const savedLocale = localStorage.getItem("locale");
    if (savedLocale && savedLocale !== locale.value) {
      (locale as WritableComputedRef<string>).value = savedLocale;
      i18n.global.locale.value = savedLocale;
      languageStore.setLocale(savedLocale);
    }
  });

  return {
    route,
    locale,
    switchLanguage
  };
}
