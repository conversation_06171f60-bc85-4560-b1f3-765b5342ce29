export type FormItemProps = {
  id?: number | null;
  uuid?: string;
  ownerType?: string;
  ownerId?: number;
  name?: string;
  type?: "file" | "text" | "url" | "document";
  storagePath?: string;
  content?: string;
  status?: "active" | "inactive" | "processing" | "failed";
  metadata?: string;
};

export type KnowledgeBaseFilterProps = {
  name?: string;
  type?: "file" | "text" | "url" | "document" | "";
  status?: "active" | "inactive" | "processing" | "failed" | "";
  ownerType?: string;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
