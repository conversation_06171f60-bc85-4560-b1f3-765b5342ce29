<script setup lang="ts">
import { computed } from "vue";
import type { FormItemProps } from "@/views/organization/utils/type";
import type { PlusColumn } from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

interface Props {
  organizationHook: any;
}

const props = defineProps<Props>();

const {
  loading,
  drawerVisible,
  drawerValues,
  handleSubmit
} = props.organizationHook;

const ruleFormRef = computed(() => props.organizationHook.organizationFormRef);

const form: PlusColumn[] = [
  {
    label: "Name",
    prop: "name",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter organization name"
    }
  },
  {
    label: "Description",
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: "Enter organization description"
    }
  },
  {
    label: "Website",
    prop: "website",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter website URL"
    }
  },
  {
    label: "Email",
    prop: "email",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter email address"
    }
  },
  {
    label: "Phone",
    prop: "phone",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter phone number"
    }
  },
  {
    label: "Address",
    prop: "address",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter address"
    }
  },
  {
    label: "City",
    prop: "city",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter city"
    }
  },
  {
    label: "State",
    prop: "state",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter state/province"
    }
  },
  {
    label: "Country",
    prop: "country",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter country"
    }
  },
  {
    label: "Postal Code",
    prop: "postalCode",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter postal code"
    }
  },
  {
    label: "Organization Type",
    prop: "organizationType",
    valueType: "select",
    options: [
      { label: "Company", value: "company" },
      { label: "Non-profit", value: "nonprofit" },
      { label: "Government", value: "government" },
      { label: "Educational", value: "educational" }
    ]
  },
  {
    label: "Industry",
    prop: "industry",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter industry"
    }
  },
  {
    label: "Employee Count",
    prop: "employeeCount",
    valueType: "input-number",
    fieldProps: {
      placeholder: "Enter employee count"
    }
  },
  {
    label: "Founded Year",
    prop: "foundedYear",
    valueType: "input-number",
    fieldProps: {
      placeholder: "Enter founded year"
    }
  },
  {
    label: "Status",
    prop: "status",
    valueType: "select",
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
      { label: "Suspended", value: "suspended" }
    ]
  },
  {
    label: "Is Verified",
    prop: "isVerified",
    valueType: "switch"
  }
];

const rules = {
  name: [
    {
      required: true,
      message: "Please enter organization name",
      trigger: "blur"
    }
  ],
  email: [
    { required: true, message: "Please enter email", trigger: "blur" },
    { type: "email", message: "Please enter valid email", trigger: "blur" }
  ],
  organizationType: [
    {
      required: true,
      message: "Please select organization type",
      trigger: "change"
    }
  ],
  status: [
    { required: true, message: "Please select status", trigger: "change" }
  ]
};
</script>

<template>
  <plus-drawer-form ref="ruleFormRef" v-model:visible="drawerVisible" v-model="drawerValues" size="40%"
    :closeOnClickModal="true" :closeOnPressEscape="true" :showClose="true" :destroyOnClose="true" :form="{
      columns: form,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }" :loading="loading" @confirm="handleSubmit">
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Organization Information") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="drawerVisible = false">
          {{ $t("Cancel") }}
        </el-button>
        <el-button plain type="primary" :loading="loading" :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit">
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </plus-drawer-form>
</template>
