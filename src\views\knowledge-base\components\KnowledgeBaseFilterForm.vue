<template>
  <plus-search
    v-model="filterRef"
    :columns="filterColumns"
    :show-number="false"
    label-width="100px"
    @search="handleFilter"
    @reset="handleFilter"
  />
</template>

<script setup lang="ts">
import type { KnowledgeBaseFilterProps } from "@/views/knowledge-base/utils/type";
import type { PlusColumn } from "plus-pro-components";

interface Props {
  knowledgeBaseHook: any;
}

const props = defineProps<Props>();

const {
  filterRef,
  handleFilter
} = props.knowledgeBaseHook;

const filterColumns: PlusColumn[] = [
  {
    label: "Name",
    prop: "name",
    valueType: "text",
    fieldProps: {
      placeholder: "Search by knowledge base name"
    }
  },
  {
    label: "Type",
    prop: "type",
    valueType: "select",
    options: [
      { label: "All", value: "" },
      { label: "File", value: "file" },
      { label: "Text", value: "text" },
      { label: "URL", value: "url" },
      { label: "Document", value: "document" }
    ],
    fieldProps: {
      placeholder: "Select type"
    }
  },
  {
    label: "Status",
    prop: "status",
    valueType: "select",
    options: [
      { label: "All", value: "" },
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
      { label: "Processing", value: "processing" },
      { label: "Failed", value: "failed" }
    ],
    fieldProps: {
      placeholder: "Select status"
    }
  },
  {
    label: "Owner Type",
    prop: "ownerType",
    valueType: "text",
    fieldProps: {
      placeholder: "Search by owner type"
    }
  },
  {
    label: "Trashed",
    prop: "isTrashed",
    valueType: "select",
    options: [
      { label: "No", value: "no" },
      { label: "Yes", value: "yes" }
    ],
    fieldProps: {
      placeholder: "Show trashed items"
    }
  }
];
</script>
