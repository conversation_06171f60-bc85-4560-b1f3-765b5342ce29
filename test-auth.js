// Test file to check setToken function with new data
// Run this file in browser console to test

// Sample data from new login API
const newLoginResponse = {
  "success": true,
  "message": "Login successful.",
  "data": {
    "user": {
      "username": "admin",
      "first_name": "<PERSON><PERSON><PERSON>",
      "last_name": "A",
      "full_name": "<PERSON>uy<PERSON>",
      "avatar": null,
      "birthday": null,
      "gender": "other",
      "email": "<EMAIL>",
      "email_verified_at": "2025-06-23T09:05:18.000000Z",
      "phone": null,
      "phone_verified_at": null,
      "address": null,
      "status": "active",
      "last_login_at": "2025-06-23T09:09:07.000000Z",
      "last_login_ip": "127.0.0.1",
      "preferences": null,
      "is_verified": true,
      "newsletter_subscribed": false,
      "created_at": "2025-06-23T09:02:27.000000Z",
      "updated_at": "2025-06-23T09:09:07.000000Z",
      "permissions": [],
      "roles": [],
      "geo_division": null,
      "country": null
    },
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vMTI3LjAuMC4xOjgwMDAvYXBpL3YxL2F1dGgvbG9naW4iLCJpYXQiOjE3NTA2Njk3NDcsImV4cCI6MTc1MDY3MzM0NywibmJmIjoxNzUwNjY5NzQ3LCJqdGkiOiJQNDU1VENiQldtSll5aGhCIiwic3ViIjoiMiIsInBydiI6ImJiNjVkOWI4ZmJmMGRhOTgyN2M4ZWQyMzFkOWM1NGM4MTdmMGZiYjIifQ.KG8FCGspvK5zZOnfmy6JHRGv55VJ-ZWjk6ywEHxusmQ",
    "token_type": "bearer",
    "expires_in": 216000
  }
};

// Test function
function testSetToken() {
  console.log('Testing setToken with new API response format...');
  
  // Import setToken function (assuming it's available in global scope)
  if (typeof setToken !== 'undefined') {
    try {
      setToken(newLoginResponse.data);
      console.log('✅ setToken executed successfully with new format');

      // Check if token has been saved
      const savedToken = getToken();
      console.log('Saved token:', savedToken);

      // Check if user info has been saved
      const userInfo = localStorage.getItem('user-info');
      console.log('Saved user info:', JSON.parse(userInfo));
      
    } catch (error) {
      console.error('❌ Error testing setToken:', error);
    }
  } else {
    console.error('❌ setToken function not found in global scope');
  }
}

// Run test
// testSetToken();

console.log('Test file loaded. Run testSetToken() to test the setToken function.');
