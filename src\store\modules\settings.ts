import { defineStore } from "pinia";
import { store } from "@/store";
import { getPublicSettings } from "@/api/system";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { storageLocal } from "@pureadmin/utils";

interface SettingsState {
  title: string;
  fixedHeader: boolean;
  hiddenSideBar: boolean;
  settings: Record<string, any>;
}

export const useSettingStore = defineStore("proCMS-setting", {
  state: (): SettingsState => ({
    title: "proCMS",
    fixedHeader: true,
    hiddenSideBar: false,
    settings: storageLocal().getItem("settings") || {}
  }),
  getters: {
    getTitle: (state): string => state.title,
    getFixedHeader: (state): boolean => state.fixedHeader,
    getHiddenSideBar: (state): boolean => state.hiddenSideBar,
    getSettings: (state): Record<string, any> => state.settings
  },
  actions: {
    setSettings(data: Record<string, any>) {
      this.settings = data;
      storageLocal().setItem("settings", data);
    },
    changeValueByKey({ key, value }) {
      if (Reflect.has(this, key)) {
        this[key] = value;
      }
    },
    async fetchPublicSettings() {
      try {
        const { data } = await getPublicSettings();
        this.setSettings(useConvertKeyToCamel(data));
      } catch (error) {
        console.error("Failed to fetch public settings:", error);
      }
    }
  }
});

export function useSettingStoreHook() {
  return useSettingStore(store);
}
