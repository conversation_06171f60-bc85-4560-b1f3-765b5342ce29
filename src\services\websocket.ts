import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { $t } from "@/plugins/i18n";

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
  id?: string;
}

export interface ChatMessage {
  id: string;
  chatId: number;
  userId: number;
  role: "user" | "assistant";
  content: string;
  contentType: "text" | "image" | "file" | "audio";
  status: "sending" | "sent" | "delivered" | "read" | "failed";
  timestamp: number;
  attachments?: any[];
}

export interface TypingIndicator {
  chatId: number;
  userId: number;
  userName: string;
  isTyping: boolean;
}

export interface ChatUpdate {
  chatId: number;
  lastMessage: ChatMessage;
  messageCount: number;
  updatedAt: string;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 1000;
  private heartbeatInterval: number | null = null;
  private messageQueue: WebSocketMessage[] = [];

  // Reactive state
  public connected = ref(false);
  public connecting = ref(false);
  public error = ref<string | null>(null);

  // Event handlers
  private eventHandlers = reactive<Record<string, Function[]>>({
    "message.new": [],
    "message.updated": [],
    "message.deleted": [],
    "typing.start": [],
    "typing.stop": [],
    "chat.updated": [],
    "user.online": [],
    "user.offline": [],
    "bot.response": []
  });

  constructor(
    private url: string,
    private token?: string
  ) {}

  // Connect to WebSocket server
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      this.connecting.value = true;
      this.error.value = null;

      try {
        const wsUrl = this.token ? `${this.url}?token=${this.token}` : this.url;

        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log("WebSocket connected");
          this.connected.value = true;
          this.connecting.value = false;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.processMessageQueue();
          resolve();
        };

        this.ws.onmessage = event => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error("Error parsing WebSocket message:", error);
          }
        };

        this.ws.onclose = event => {
          console.log("WebSocket disconnected:", event.code, event.reason);
          this.connected.value = false;
          this.connecting.value = false;
          this.stopHeartbeat();

          if (
            !event.wasClean &&
            this.reconnectAttempts < this.maxReconnectAttempts
          ) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = error => {
          console.error("WebSocket error:", error);
          this.error.value = $t("WebSocket connection error");
          this.connecting.value = false;
          reject(error);
        };
      } catch (error) {
        this.connecting.value = false;
        this.error.value = $t("Failed to create WebSocket connection");
        reject(error);
      }
    });
  }

  // Disconnect from WebSocket server
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, "Client disconnect");
      this.ws = null;
    }
    this.stopHeartbeat();
    this.connected.value = false;
    this.connecting.value = false;
  }

  // Send message through WebSocket
  send(type: string, data: any): void {
    const message: WebSocketMessage = {
      type,
      data,
      timestamp: Date.now(),
      id: this.generateMessageId()
    };

    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Queue message for later sending
      this.messageQueue.push(message);
      if (!this.connected.value && !this.connecting.value) {
        this.connect().catch(console.error);
      }
    }
  }

  // Subscribe to events
  on(event: string, handler: Function): void {
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = [];
    }
    this.eventHandlers[event].push(handler);
  }

  // Unsubscribe from events
  off(event: string, handler?: Function): void {
    if (!this.eventHandlers[event]) return;

    if (handler) {
      const index = this.eventHandlers[event].indexOf(handler);
      if (index > -1) {
        this.eventHandlers[event].splice(index, 1);
      }
    } else {
      this.eventHandlers[event] = [];
    }
  }

  // Send chat message
  sendMessage(
    chatId: number,
    content: string,
    contentType: string = "text",
    attachments?: any[]
  ): void {
    this.send("message.send", {
      chatId,
      content,
      contentType,
      attachments
    });
  }

  // Send typing indicator
  sendTyping(chatId: number, isTyping: boolean): void {
    this.send("typing", {
      chatId,
      isTyping
    });
  }

  // Join chat room
  joinChat(chatId: number): void {
    this.send("chat.join", { chatId });
  }

  // Leave chat room
  leaveChat(chatId: number): void {
    this.send("chat.leave", { chatId });
  }

  // Mark message as read
  markAsRead(messageId: string): void {
    this.send("message.read", { messageId });
  }

  // Private methods
  private handleMessage(message: WebSocketMessage): void {
    const handlers = this.eventHandlers[message.type] || [];
    handlers.forEach(handler => {
      try {
        handler(message.data);
      } catch (error) {
        console.error(`Error in ${message.type} handler:`, error);
      }
    });
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = window.setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.send("ping", {});
      }
    }, 30000); // Send ping every 30 seconds
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay =
      this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);

    console.log(
      `Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`
    );

    setTimeout(() => {
      if (!this.connected.value) {
        this.connect().catch(console.error);
      }
    }, delay);
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message && this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(message));
      }
    }
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Create singleton instance
const wsUrl = import.meta.env.VITE_WS_URL || "ws://localhost:8080/ws";
export const websocketService = new WebSocketService(wsUrl);

// Auto-connect when user is authenticated
export const initializeWebSocket = (token?: string) => {
  if (token) {
    websocketService.connect().catch(error => {
      console.error("Failed to connect to WebSocket:", error);
      ElMessage.error($t("Failed to connect to real-time service"));
    });
  }
};

// Cleanup on page unload
window.addEventListener("beforeunload", () => {
  websocketService.disconnect();
});
