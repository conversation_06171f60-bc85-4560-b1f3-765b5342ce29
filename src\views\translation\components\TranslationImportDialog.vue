<script setup lang="ts">
import { ref } from "vue";
import { $t } from "@/plugins/i18n";
import {
  ElDialog,
  ElUpload,
  ElButton,
  ElIcon,
  ElText,
  ElMessage
} from "element-plus";
import { Upload, Download } from "@element-plus/icons-vue";

interface Props {
  visible: boolean;
  loading?: boolean;
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "import", file: File): void;
  (e: "download-template"): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loading: false
});

const emit = defineEmits<Emits>();

const uploadRef = ref();
const fileList = ref([]);

const handleFileChange = (file: any) => {
  fileList.value = [file];
  return false; // Prevent auto upload
};

const handleRemove = () => {
  fileList.value = [];
};

const handleImport = () => {
  if (fileList.value.length === 0) {
    return;
  }
  const file = fileList.value[0].raw || fileList.value[0];
  emit("import", file);
};

const handleDownloadTemplate = () => {
  emit("download-template");
};

const handleClose = () => {
  fileList.value = [];
  emit("update:visible", false);
};

const beforeUpload = (file: File) => {
  const isExcel =
    file.type ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
    file.type === "application/vnd.ms-excel" ||
    file.name.endsWith(".xlsx") ||
    file.name.endsWith(".xls");

  if (!isExcel) {
    ElMessage.error($t("Please upload Excel file (.xlsx or .xls)"));
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error($t("File size cannot exceed 10MB"));
    return false;
  }

  return true;
};
</script>

<template>
  <ElDialog
    :model-value="visible"
    :title="$t('Import Translations')"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @update:model-value="emit('update:visible', $event)"
    @close="handleClose"
  >
    <div class="space-y-4">
      <div class="text-sm text-gray-600">
        <p>{{ $t("Please follow these steps to import translations:") }}</p>
        <ol class="list-decimal list-inside mt-2 space-y-1">
          <li>{{ $t("Download the template file") }}</li>
          <li>{{ $t("Fill in your translation data") }}</li>
          <li>{{ $t("Upload the completed file") }}</li>
        </ol>
      </div>

      <div class="flex justify-center">
        <ElButton
          type="primary"
          :icon="Download"
          @click="handleDownloadTemplate"
        >
          {{ $t("Download Template") }}
        </ElButton>
      </div>

      <div class="border-t pt-4">
        <ElUpload
          ref="uploadRef"
          v-model:file-list="fileList"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :on-remove="handleRemove"
          :auto-upload="false"
          :limit="1"
          accept=".xlsx,.xls"
          drag
        >
          <ElIcon class="el-icon--upload">
            <Upload />
          </ElIcon>
          <div class="el-upload__text">
            {{ $t("Drop file here or") }} <em>{{ $t("click to upload") }}</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              {{
                $t(
                  "Only Excel files (.xlsx, .xls) are supported, max size 10MB"
                )
              }}
            </div>
          </template>
        </ElUpload>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <ElButton @click="handleClose">
          {{ $t("Cancel") }}
        </ElButton>
        <ElButton
          type="primary"
          :loading="loading"
          :disabled="fileList.length === 0"
          @click="handleImport"
        >
          {{ $t("Import") }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.el-upload-dragger {
  width: 100%;
}
</style>
