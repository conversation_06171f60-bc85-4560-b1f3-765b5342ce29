<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { $t } from "@/plugins/i18n";
import { ElAvatar, ElOption, ElSelect } from "element-plus";
import { h } from "vue";

interface User {
  id: number;
  name: string;
  email: string;
  avatar?: string;
}

interface Props {
  modelValue?: number | number[] | null;
  multiple?: boolean;
  placeholder?: string;
  clearable?: boolean;
  filterable?: boolean;
  disabled?: boolean;
  size?: "large" | "default" | "small";
  users?: User[];
  loading?: boolean;
  remote?: boolean;
  remoteMethod?: (query: string) => Promise<User[]>;
}

interface Emits {
  (e: "update:modelValue", value: number | number[] | null): void;
  (e: "change", value: number | number[] | null): void;
  (e: "focus"): void;
  (e: "blur"): void;
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  placeholder: "",
  clearable: true,
  filterable: true,
  disabled: false,
  size: "default",
  users: () => [],
  loading: false,
  remote: false
});

const emit = defineEmits<Emits>();

const internalUsers = ref<User[]>([]);
const remoteLoading = ref(false);

const placeholderText = computed(() => {
  if (props.placeholder) return props.placeholder;
  return props.multiple ? $t("Select users") : $t("Select user");
});

const allUsers = computed(() => {
  return props.remote ? internalUsers.value : props.users;
});

// Handle remote search
const handleRemoteSearch = async (query: string) => {
  if (!props.remote || !props.remoteMethod) return;
  
  if (!query) {
    internalUsers.value = [];
    return;
  }

  remoteLoading.value = true;
  try {
    const users = await props.remoteMethod(query);
    internalUsers.value = users;
  } catch (error) {
    console.error("Error searching users:", error);
    internalUsers.value = [];
  } finally {
    remoteLoading.value = false;
  }
};

// Custom option renderer with avatar
const renderOption = (user: User) => {
  return h("div", { class: "flex items-center space-x-2" }, [
    h(ElAvatar, {
      size: 24,
      src: user.avatar,
      alt: user.name
    }, {
      default: () => user.name.charAt(0).toUpperCase()
    }),
    h("div", { class: "flex flex-col flex-1 min-w-0" }, [
      h("span", { class: "text-sm font-medium truncate" }, user.name),
      h("span", { class: "text-xs text-gray-500 truncate" }, user.email)
    ])
  ]);
};

// Handle value changes
const handleChange = (value: number | number[] | null) => {
  emit("update:modelValue", value);
  emit("change", value);
};

const handleFocus = () => {
  emit("focus");
};

const handleBlur = () => {
  emit("blur");
};

onMounted(() => {
  if (props.remote && props.users.length > 0) {
    internalUsers.value = props.users;
  }
});
</script>

<template>
  <el-select
    :model-value="modelValue"
    :multiple="multiple"
    :placeholder="placeholderText"
    :clearable="clearable"
    :filterable="filterable"
    :disabled="disabled"
    :size="size"
    :loading="loading || remoteLoading"
    :remote="remote"
    :remote-method="remote ? handleRemoteSearch : undefined"
    :reserve-keyword="remote"
    class="user-selector"
    @update:model-value="handleChange"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <el-option
      v-for="user in allUsers"
      :key="user.id"
      :label="user.name"
      :value="user.id"
      class="user-option"
    >
      <div class="flex items-center space-x-2">
        <el-avatar 
          :size="24"
          :src="user.avatar"
          :alt="user.name"
        >
          {{ user.name.charAt(0).toUpperCase() }}
        </el-avatar>
        <div class="flex flex-col flex-1 min-w-0">
          <span class="text-sm font-medium truncate">{{ user.name }}</span>
          <span class="text-xs text-gray-500 truncate">{{ user.email }}</span>
        </div>
      </div>
    </el-option>

    <template v-if="allUsers.length === 0 && !loading && !remoteLoading">
      <el-option
        disabled
        :label="remote ? $t('Type to search users') : $t('No users available')"
        value=""
      />
    </template>
  </el-select>
</template>

<style scoped>
.user-selector {
  width: 100%;
}

:deep(.el-select__wrapper) {
  transition: all 0.3s ease;
}

:deep(.el-select__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-select__wrapper.is-focused) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

.user-option {
  height: auto !important;
  padding: 8px 12px !important;
}

:deep(.user-option .el-select-dropdown__item) {
  height: auto;
  padding: 8px 12px;
}
</style>
