import { ref, reactive, computed, nextTick, watch, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useChatBotStoreHook } from "@/store/modules/chat";
import {
  sendAndRespond,
  createConversation,
  deleteConversation,
  getConversations,
  getMessages
} from "@/views/chat/utils/auth-api";
import { useSoketi } from "@/services/soketi.service";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { message } from "@/utils/message";

export function useChatBot() {
  // Store
  const chatBotStore = useChatBotStoreHook();

  // State management
  const messages = reactive<{ [conversationId: string]: any[] }>({});
  const currentConversationId = ref<string | null>(null);
  const newMessage = ref("");
  const isTyping = ref(false);
  const attachedFiles = ref([]);
  const searchQuery = ref("");
  const filterType = ref("all");
  const conversations = ref<{ [botUuid: string]: any[] }>({});
  const { subscribe, unsubscribe } = useSoketi();

  const showCurrentAgentHistory = ref(false);
  const chatMessagesContainer = ref<HTMLElement | null>(null);

  // Computed properties
  const selectedAgent = computed(() => chatBotStore.selectedBot);
  const agents = computed(() => [...chatBotStore.bots]);

  const currentMessages = computed(() => {
    if (!currentConversationId.value) return [];
    return messages[currentConversationId.value] || [];
  });

  const showStarterPrompts = computed(() => {
    if (!selectedAgent.value) return false;

    // Hiển thị starter prompts khi:
    // 1. Không có conversation hiện tại (chưa bắt đầu chat)
    // 2. Hoặc conversation chỉ có greeting message
    if (!currentConversationId.value) return true;

    const messages = currentMessages.value;
    return (
      messages.length <= 1 &&
      (messages.length === 0 ||
        (messages.length === 1 && messages[0].role === "assistant"))
    );
  });

  const filteredAgents = computed(() => {
    let agentsToFilter = agents.value;
    if (filterType.value !== "all") {
      agentsToFilter = agentsToFilter.filter(
        agent => agent.botType === filterType.value
      );
    }
    if (!searchQuery.value) {
      return agentsToFilter;
    }
    return agentsToFilter.filter(
      agent =>
        agent.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        agent.description
          .toLowerCase()
          .includes(searchQuery.value.toLowerCase())
    );
  });

  // Helper function để lấy conversations của bot
  const getConversationsForBot = async (botUuid: string) => {
    try {
      const response = await getConversations({
        bot_uuid: botUuid
      });
      if (response.success && response.data) {
        conversations.value[botUuid] = useConvertKeyToCamel(response.data);
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error fetching conversations for bot:", error);
      return [];
    }
  };

  // Helper function để load messages của conversation
  const loadConversationMessages = async (
    conversationId: string,
    page: number = 1
  ) => {
    try {
      const response = await getMessages(conversationId, { page });
      if (response.success && response.data) {
        // Convert API messages to local format
        const formattedMessages = response.data.map((msg: any) => ({
          id: msg.id,
          role: msg.role,
          content: msg.content
        }));
        messages[conversationId] = formattedMessages;
        return formattedMessages;
      }
      return [];
    } catch (error) {
      console.error("Error loading conversation messages:", error.messages);
      return [];
    }
  };

  // Helper function để kiểm tra và load conversation tồn tại
  const checkAndLoadExistingConversation = async (botUuid: string) => {
    try {
      // Ưu tiên kiểm tra conversation có sẵn trong bot data trước
      if (selectedAgent.value?.conversations?.length > 0) {
        const existingConversation = selectedAgent.value.conversations[0];
        const conversationId = existingConversation.uuid;

        // Set current conversation
        currentConversationId.value = conversationId;

        // Load messages của conversation này
        const conversationMessages =
          await loadConversationMessages(conversationId);

        if (conversationMessages.length === 0) {
          messages[conversationId] = [];
        }

        return conversationId;
      }

      // Nếu không có conversation trong bot data, tìm từ API
      return await createNewConversation(botUuid);
    } catch (error) {
      console.error("Error checking existing conversation:", error);
      return null;
    }
  };

  // Helper function để tạo conversation mới
  const createNewConversation = async (botUuid: string) => {
    try {
      // Gọi API để tạo conversation mới
      const response = await createConversation({
        title: `Cuộc trò chuyện với ${selectedAgent.value?.name || "Bot"}`,
        bot_uuid: botUuid
      });

      if (response.success && response.data) {
        const newConversationId = response.data.uuid.toString();
        currentConversationId.value = newConversationId;

        // Cập nhật conversation UUID trong selectedAgent
        if (selectedAgent.value) {
          messages[newConversationId] = [
            {
              id: `greeting-${newConversationId}`,
              role: "assistant",
              content:
                selectedAgent.value.greetingMessage ||
                `Xin chào! Bạn cần tôi giúp gì với ${selectedAgent.value.name}?`
            }
          ];
          chatBotStore.setConversation({
            uuid: response.data.uuid || response.data.id?.toString(),
            title:
              response.data.title ||
              `Cuộc trò chuyện với ${selectedAgent.value.name}`,
            botUuid: botUuid,
            messageCount: 0,
            lastMessageAt: new Date().toISOString()
          });
        }
        return newConversationId;
      } else {
        throw new Error("Failed to create conversation");
      }
    } catch (error) {
      console.error("Error creating conversation:", error);
      ElMessage.error("Lỗi khi tạo cuộc trò chuyện mới");

      const fallbackId = `conv-${botUuid}-${Date.now()}`;
      currentConversationId.value = fallbackId;

      messages[fallbackId] = [];

      return fallbackId;
    }
  };

  // API call để gửi tin nhắn
  const sendMessageToServer = async (prompt: string) => {
    try {
      if (!selectedAgent.value || !currentConversationId.value) {
        return;
      }

      return await sendAndRespond({
        conversationUuid: currentConversationId.value.toString(),
        content: prompt
      });
    } catch (error) {
      console.error("Error sending message to server:", error);
      ElMessage.error("Lỗi khi gửi tin nhắn đến server");
      throw error;
    }
  };

  // Trigger agent response
  const triggerAgentResponse = async (text: string) => {
    if (!selectedAgent.value || isTyping.value) return;

    isTyping.value = true;

    scrollToBottom();

    try {
      await sendMessageToServer(text);
      // Server sẽ xử lý và gửi response qua WebSocket
    } catch (error) {
      console.error("Error in triggerAgentResponse:", error);
      isTyping.value = false;
      ElMessage.error("Lỗi khi gửi tin nhắn");
    }
  };

  // Bắt đầu conversation mới khi cần thiết
  const ensureConversationExists = async () => {
    if (!selectedAgent.value) return null;

    // Nếu đã có conversation hiện tại, return luôn
    if (currentConversationId.value) {
      return currentConversationId.value;
    }

    console.log(
      "sendMessageToServer-------------------->:",
      currentConversationId.value
    );
    // return await createNewConversation(selectedAgent.value.uuid);
    return;
  };

  // Gửi tin nhắn
  const sendMessage = async () => {
    const text = newMessage.value.trim();
    const files = attachedFiles.value;
    if ((!text && files.length === 0) || !selectedAgent.value) return;

    // Đảm bảo có conversation trước khi gửi tin nhắn
    const conversationId = await ensureConversationExists();
    if (!conversationId) {
      ElMessage.error("Không thể tạo cuộc trò chuyện");
      return;
    }

    let userMessageContent = text;
    if (files.length > 0) {
      userMessageContent += `\n(Đã đính kèm ${files.length} tệp)`;
    }

    console.log(
      "Sending message uuid conversation------------>:",
      conversationId
    );

    // Thêm tin nhắn user vào conversation hiện tại
    messages[conversationId].push({
      id: Date.now(),
      role: "user",
      content: userMessageContent
    });

    const textToSend = newMessage.value.trim();
    newMessage.value = "";
    attachedFiles.value = [];

    await triggerAgentResponse(textToSend);
  };

  // Gửi starter prompt
  const sendStarterPrompt = async (promptText: string) => {
    if (!selectedAgent.value) return;

    // Đảm bảo có conversation trước khi gửi starter prompt
    const conversationId = await ensureConversationExists();
    if (!conversationId) {
      ElMessage.error("Không thể tạo cuộc trò chuyện");
      return;
    }

    messages[conversationId].push({
      id: Date.now(),
      role: "user",
      content: promptText
    });

    await triggerAgentResponse(promptText);
  };

  // Xóa tin nhắn trong conversation (chỉ clear messages)
  const clearMessages = () => {
    if (!selectedAgent.value || !currentConversationId.value) return;

    console.log("clearMessages called - showing confirmation dialog");

    ElMessageBox.confirm(
      "Bạn có chắc chắn muốn xóa tất cả tin nhắn trong cuộc trò chuyện này không?",
      "Xác nhận xóa tin nhắn",
      {
        confirmButtonText: "Xóa tin nhắn",
        cancelButtonText: "Hủy",
        type: "warning"
      }
    )
      .then(() => {
        if (currentConversationId.value) {
          // Chỉ clear messages, welcome message sẽ hiển thị trong UI
          messages[currentConversationId.value] = [];
        }
        ElMessage.success("Đã xóa tất cả tin nhắn");
      })
      .catch(() => {});
  };

  // Xóa hoàn toàn conversation
  const deleteConversationByUuid = async (uuid: string, idx: number = -1) => {
    if (idx === -1) return;

    await deleteConversation(uuid)
      .then(res => {
        console.log("ES---------------------->:::", res);
        if (res.success) {
          conversations.value[selectedAgent.value?.uuid]?.splice(idx, 1);
          if (conversations.value[selectedAgent.value?.uuid].length === 0) {
            showCurrentAgentHistory.value = false;
            createNewConversation(selectedAgent.value?.uuid);
          }
          message(res.message, { type: "success" });
        }
      })
      .catch(e => {
        message(e?.response?.data?.message || e?.message, { type: "error" });
      });
  };

  // Chọn agent
  const selectAgent = (agentId: string) => {
    chatBotStore.getSelectedChatBot(agentId);
  };

  // Xóa attachment
  const removeAttachment = (index: number) => {
    attachedFiles.value.splice(index, 1);
  };

  // WebSocket event handler cho bot response
  const handleBotResponse = (data: any) => {
    console.log("Received bot response:", data);

    // Validate dữ liệu nhận được
    if (!data || !data.content) {
      console.warn("Invalid bot response data:", data);
      isTyping.value = false;
      return;
    }

    // Nếu server gửi kèm conversationId, validate nó
    if (
      data.conversationId &&
      data.conversationId !== currentConversationId.value
    ) {
      isTyping.value = false;
      return;
    }

    // Đảm bảo có conversation hiện tại
    if (!currentConversationId.value) {
      isTyping.value = false;
      return;
    }

    // Thêm response vào conversation hiện tại
    messages[currentConversationId.value].push({
      id: data.messageId || Date.now() + 1,
      role: "assistant",
      content: data.content
    });

    // Tắt typing indicator
    isTyping.value = false;
  };

  // Watch selectedAgent để load conversation hiện có
  watch(
    selectedAgent,
    async newAgent => {
      if (newAgent) {
        const existingConversationId = await checkAndLoadExistingConversation(
          newAgent.uuid
        );
        if (!existingConversationId) {
          currentConversationId.value = null;
        }
        if (!conversations[newAgent.uuid]) {
          getConversationsForBot(newAgent.uuid).catch();
        }
        scrollToBottom();
      }
    },
    { immediate: true }
  );

  // Scroll to bottom function
  const scrollToBottom = () => {
    nextTick(() => {
      if (chatMessagesContainer.value) {
        chatMessagesContainer.value.scrollTo({
          top: chatMessagesContainer.value.scrollHeight,
          behavior: "smooth"
        });
      }
    });
  };

  watch(
    currentConversationId,
    (newId, oldId) => {
      console.log(`Chat ID changed from: ${oldId} -> to: ${newId}`);

      if (oldId) {
        const oldChannel = `private-conversation.${oldId}`;
        unsubscribe(oldChannel);
      }

      if (newId) {
        const newChannel = `private-conversation.${newId}`;
        subscribe(newChannel, "message.received", (data: any) => {
          messages[newId]
            ? messages[newId].push(data)
            : (messages[newId] = [data]);
          isTyping.value = false;
          scrollToBottom();
        });
      }
    },
    {
      immediate: true
    }
  );

  onUnmounted(() => {
    if (currentConversationId.value) {
      const lastChannel = `private-conversation.${currentConversationId.value}`;
      unsubscribe(lastChannel);
    }
  });

  return {
    // State
    messages,
    conversations,
    currentConversationId,
    newMessage,
    isTyping,
    attachedFiles,
    searchQuery,
    filterType,
    chatMessagesContainer,
    showCurrentAgentHistory,

    // Computed
    selectedAgent,
    agents,
    currentMessages,
    showStarterPrompts,
    filteredAgents,

    // Methods
    createNewConversation,
    checkAndLoadExistingConversation,
    getConversationsForBot,
    loadConversationMessages,
    ensureConversationExists,
    sendMessage,
    sendStarterPrompt,
    clearMessages,
    selectAgent,
    removeAttachment,
    triggerAgentResponse,
    sendMessageToServer,
    scrollToBottom,
    deleteConversationByUuid,

    // WebSocket
    handleBotResponse
  };
}
