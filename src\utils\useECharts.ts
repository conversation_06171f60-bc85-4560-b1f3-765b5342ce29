import { ref, onMounted, onUnmounted, nextTick, type Ref, getCurrentInstance } from "vue";
import echarts from "@/plugins/echarts";

export function useECharts(chartRef: Ref<HTMLElement | undefined>) {
  const chartInstance = ref<any>(null);
  const isLoading = ref(false);

  const initChart = async () => {
    console.log('initChart called', { chartRef: chartRef.value, echarts: !!echarts });

    if (!chartRef.value) {
      console.warn('Chart ref is not available');
      return;
    }

    // Check if element has dimensions
    const rect = chartRef.value.getBoundingClientRect();
    console.log('Chart container dimensions:', rect);

    if (rect.width === 0 || rect.height === 0) {
      console.warn('Chart container has no dimensions, waiting...');
      setTimeout(() => initChart(), 100);
      return;
    }

    await nextTick();

    if (chartInstance.value) {
      chartInstance.value.dispose();
    }

    try {
      chartInstance.value = echarts.init(chartRef.value);
      console.log('Chart instance created successfully', chartInstance.value);
      return chartInstance.value;
    } catch (error) {
      console.error('Error creating chart instance:', error);
    }
    
    // Add resize listener
    const resizeObserver = new ResizeObserver(() => {
      chartInstance.value?.resize();
    });
    
    resizeObserver.observe(chartRef.value);
    
    return chartInstance.value;
  };

  const setOptions = (options: any) => {
    console.log('setOptions called', { echarts: !!echarts, chartInstance: !!chartInstance.value, options });

    if (!chartInstance.value) {
      console.log('Chart instance not available, initializing...');
      initChart().then(() => {
        if (chartInstance.value) {
          console.log('Setting options after initialization');
          chartInstance.value.setOption(options);
        } else {
          console.error('Chart instance still not available after initialization');
        }
      });
    } else {
      console.log('Setting options on existing chart instance');
      chartInstance.value.setOption(options);
    }
  };

  const resize = () => {
    chartInstance.value?.resize();
  };

  const dispose = () => {
    if (chartInstance.value) {
      chartInstance.value.dispose();
      chartInstance.value = null;
    }
  };

  const showLoading = () => {
    isLoading.value = true;
    chartInstance.value?.showLoading();
  };

  const hideLoading = () => {
    isLoading.value = false;
    chartInstance.value?.hideLoading();
  };

  onMounted(() => {
    // Wait for DOM to be ready
    nextTick(() => {
      if (chartRef.value) {
        initChart();
      }
    });
  });

  onUnmounted(() => {
    dispose();
  });

  return {
    chartInstance,
    isLoading,
    initChart,
    setOptions,
    resize,
    dispose,
    showLoading,
    hideLoading
  };
}
