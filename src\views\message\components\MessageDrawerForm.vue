<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getChats } from "../utils/auth-api";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);
const formRef = ref();
const chats = ref([]);

onMounted(async () => {
  console.log("Message------------>:", props.values);
  await loadChats();
});

const loadChats = async () => {
  try {
    const { data } = await getChats();
    if (data?.success) {
      chats.value = data.data.map((chat: any) => ({
        label: chat.title || `Chat #${chat.id}`,
        value: chat.id
      }));
    }
  } catch (error) {
    console.error("Error loading chats:", error);
  }
};

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Chat")),
    prop: "chatId",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select a chat"),
        trigger: ["change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Select chat"),
      clearable: true,
      filterable: true
    },
    options: computed(() => chats.value)
  },
  {
    label: computed(() => $t("Role")),
    prop: "role",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select message role"),
        trigger: ["change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Select role")
    },
    options: [
      { label: $t("User"), value: "user" },
      { label: $t("Assistant"), value: "assistant" },
      { label: $t("System"), value: "system" }
    ]
  },
  {
    label: computed(() => $t("Content Type")),
    prop: "contentType",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select content type"),
        trigger: ["change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Select content type")
    },
    options: [
      { label: $t("Text"), value: "text" },
      { label: $t("Image"), value: "image" },
      { label: $t("File"), value: "file" },
      { label: $t("Audio"), value: "audio" }
    ]
  },
  {
    label: computed(() => $t("Content")),
    prop: "content",
    valueType: "textarea",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input message content"),
        trigger: ["blur"]
      },
      {
        min: 1,
        max: 10000,
        message: $t("Length must be between 1 and 10000 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: $t("Enter message content"),
      maxlength: 10000,
      showWordLimit: true,
      autosize: { minRows: 3, maxRows: 8 }
    }
  }
];

const handleSubmit = async (values: FieldValues) => {
  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="50%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Message Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit(values)"
        >
          {{ $t("Submit") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style scoped>
.custom-group-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  width: 100%;
}
</style>
