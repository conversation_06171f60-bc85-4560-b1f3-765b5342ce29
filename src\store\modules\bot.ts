import { defineStore } from "pinia";
import { store } from "@/store";

import { useConvertKeyToCamel } from "@/utils/helpers";
import { getBots } from "@/views/bot/utils/api";
import { storageLocal } from "@pureadmin/utils";

export interface Bot {
  uuid: string;
  name: string;
  description?: string;
  status: string;
  visibility: string;
  botType: string;
}

interface BotState {
  bots: Bot[] | null;
  loading: boolean;
  selectedBot: Bot | null;
}

export const useBotStore = defineStore("proCMS-bot", {
  state: (): BotState => {
    const bots = storageLocal().getItem<Bot[]>("bots");
    return {
      bots,
      loading: false,
      selectedBot: null
    };
  },

  getters: {
    getBots: state => state.bots
  },

  actions: {
    setBots(data: any) {
      this.bots = data;
    },
    setSelectedBot(bot: Bot | null) {
      this.selectedBot = bot;
    },
    async fetchPublicBots() {
      try {
        const { data } = await getBots();
        this.setBots(useConvertKeyToCamel(data));
      } catch (error) {
        console.error("Failed to fetch bots:", error);
      }
    }
  }
});

export function useBotStoreHook() {
  return useBotStore(store);
}
