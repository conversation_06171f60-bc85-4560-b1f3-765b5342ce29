// Test complete authentication flow
console.log("=== Full Authentication Flow Test ===");

// 1. Login response (new format from real API)
const loginResponse = {
  "success": true,
  "message": "Login successful.",
  "data": {
    "user": {
      "username": "admin",
      "first_name": "<PERSON><PERSON><PERSON>",
      "last_name": "A",
      "full_name": "<PERSON>uy<PERSON>",
      "permissions": ["*:*:*"],
      "roles": ["admin"]
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.login_token",
    "token_type": "Bearer",
    "expires_in": 60 // 1 minute for testing
  }
};

// 2. Refresh token response (old format from mock)
const refreshResponse = {
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzUxMiJ9.newAdmin",
    "refreshToken": "eyJhbGciOiJIUzUxMiJ9.newAdminRefresh",
    "expires": "2030/10/30 23:59:59" // String format
  }
};

// Helper functions
function convertSnakeToCamel(obj) {
  if (!obj) return obj;
  const convertKey = (key) => key.replace(/_([a-z])/g, (_, char) => char.toUpperCase());
  const convertObjectKeys = (o) => {
    if (Array.isArray(o)) {
      return o.map(item => convertObjectKeys(item));
    } else if (o !== null && o !== undefined && o.constructor === Object) {
      return Object.keys(o).reduce((acc, key) => {
        const camelKey = convertKey(key);
        acc[camelKey] = convertObjectKeys(o[key]);
        return acc;
      }, {});
    }
    return o;
  };
  return convertObjectKeys(obj);
}

function simulateSetToken(data) {
  console.log("\n=== setToken Simulation ===");
  console.log("Input data:", JSON.stringify(data, null, 2));
  
  let expires = 0;
  let accessToken;
  let refreshToken;
  
  // Check if data is from new login API response format
  if ('token' in data && 'user' in data) {
    console.log("Detected: New API format (login)");
    accessToken = data.token;
    refreshToken = data.token; // Use same token as refresh token for now
    expires = Date.now() + (data.expiresIn * 1000);
  } else {
    console.log("Detected: Old format (refresh token)");
    accessToken = data.accessToken;
    refreshToken = data.refreshToken;
    expires = new Date(data.expires).getTime();
  }
  
  console.log("Processed token data:");
  console.log("- accessToken:", accessToken);
  console.log("- refreshToken:", refreshToken);
  console.log("- expires timestamp:", expires);
  console.log("- expires date:", new Date(expires).toISOString());
  
  return { accessToken, refreshToken, expires };
}

function simulateTokenCheck(tokenData) {
  const now = new Date().getTime();
  const expired = (typeof tokenData.expires === 'number' ? tokenData.expires : parseInt(tokenData.expires)) - now <= 0;
  
  console.log("\n=== Token Check ===");
  console.log("Current time:", new Date(now).toISOString());
  console.log("Token expires:", new Date(tokenData.expires).toISOString());
  console.log("Time remaining (seconds):", (tokenData.expires - now) / 1000);
  console.log("Is expired:", expired);
  
  return expired;
}

// Test flow
console.log("\n=== Step 1: Login ===");
const convertedLoginData = convertSnakeToCamel(loginResponse.data);
const loginTokenData = simulateSetToken(convertedLoginData);

console.log("\n=== Step 2: Check token immediately after login ===");
simulateTokenCheck(loginTokenData);

console.log("\n=== Step 3: Simulate token expiry (after 65 seconds) ===");
const expiredTime = Date.now() + 65000;
console.log("Simulated time:", new Date(expiredTime).toISOString());
const wouldBeExpired = loginTokenData.expires - expiredTime <= 0;
console.log("Would be expired:", wouldBeExpired);

if (wouldBeExpired) {
  console.log("\n=== Step 4: Refresh token triggered ===");
  console.log("Calling refresh token API...");
  
  // Simulate refresh token call
  const refreshTokenData = simulateSetToken(refreshResponse.data);
  
  console.log("\n=== Step 5: Check new token after refresh ===");
  simulateTokenCheck(refreshTokenData);
  
  console.log("\n=== Token refresh flow completed successfully! ===");
} else {
  console.log("\n=== Token still valid, no refresh needed ===");
}

// Test potential issues
console.log("\n=== Potential Issues Analysis ===");

console.log("\n1. Format Consistency:");
console.log("- Login API returns: snake_case with expiresIn (number)");
console.log("- Refresh API returns: camelCase with expires (string)");
console.log("- This inconsistency could cause issues!");

console.log("\n2. Token Expiry Calculation:");
console.log("- Login: Date.now() + (expiresIn * 1000) ✓");
console.log("- Refresh: new Date(expires).getTime() ✓");
console.log("- Both should work correctly");

console.log("\n3. Refresh Token Logic:");
console.log("- Uses same token as refresh token initially");
console.log("- This might not work with real API");
console.log("- Real API should provide separate refresh token");

console.log("\n=== Recommendations ===");
console.log("1. Ensure API consistency (all camelCase or all snake_case)");
console.log("2. Use proper refresh token from login response");
console.log("3. Add error handling for refresh token failures");
console.log("4. Consider token auto-refresh before expiry (e.g., 5 minutes before)");

console.log("\n=== Test Complete ===");
