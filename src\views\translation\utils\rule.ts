import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const translationRules = reactive<FormRules>({
  key: [
    {
      required: true,
      message: $t("Please enter the translation key"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 255,
      message: $t("Length must be between 2 and 255 characters"),
      trigger: "blur"
    },
    {
      pattern: /^[a-zA-Z0-9._-]+$/,
      message: $t("Key can only contain letters, numbers, dots, underscores and hyphens"),
      trigger: "blur"
    }
  ],
  languageCode: [
    {
      required: true,
      message: $t("Please select the language"),
      trigger: "change"
    }
  ],
  value: [
    {
      required: true,
      message: $t("Please enter the translation value"),
      trigger: "blur"
    },
    {
      max: 5000,
      message: $t("Translation value cannot exceed 5000 characters"),
      trigger: "blur"
    }
  ],
  group: [
    {
      max: 100,
      message: $t("Group name cannot exceed 100 characters"),
      trigger: "blur"
    }
  ]
});

export { translationRules };
