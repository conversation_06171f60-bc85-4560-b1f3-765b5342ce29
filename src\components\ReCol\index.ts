import { ElCol } from "element-plus";
import { h, defineComponent } from "vue";

// Wrapper for element-plus el-col component
export default defineComponent({
  name: "<PERSON><PERSON><PERSON>",
  props: {
    value: {
      type: Number,
      default: 24
    }
  },
  render() {
    const attrs = this.$attrs;
    const val = this.value;
    return h(
      ElCol,
      {
        xs: val,
        sm: val,
        md: val,
        lg: val,
        xl: val,
        ...attrs
      },
      { default: () => this.$slots.default() }
    );
  }
});
