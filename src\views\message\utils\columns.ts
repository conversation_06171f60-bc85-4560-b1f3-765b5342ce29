import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag, ElAvatar, ElTooltip } from "element-plus";
import { h } from "vue";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "chat",
    align: "left",
    width: 180,
    headerRenderer: () => $t("Chat"),
    cellRenderer: ({ row }) => {
      if (!row.chat) return "-";
      return h("div", { class: "flex flex-col" }, [
        h("span", { class: "text-sm font-medium" }, row.chat.title || `Chat #${row.chat.id}`),
        h("span", { class: "text-xs text-gray-500" }, 
          `${row.chat.user?.name || 'Unknown'} ↔ ${row.chat.bot?.name || 'Bot'}`
        )
      ]);
    }
  },
  {
    prop: "role",
    align: "center",
    width: 80,
    headerRenderer: () => $t("Role"),
    cellRenderer: ({ row }) => {
      const roleColors = {
        user: "primary",
        assistant: "success",
        system: "warning"
      };
      const roleIcons = {
        user: "👤",
        assistant: "🤖",
        system: "⚙️"
      };
      return h(
        ElTag,
        {
          type: roleColors[row.role] || "info",
          size: "small"
        },
        () => `${roleIcons[row.role] || ""} ${row.role?.toUpperCase() || "USER"}`
      );
    }
  },
  {
    prop: "contentType",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Type"),
    cellRenderer: ({ row }) => {
      const typeColors = {
        text: "info",
        image: "success",
        file: "warning",
        audio: "primary"
      };
      const typeIcons = {
        text: "📝",
        image: "🖼️",
        file: "📎",
        audio: "🎵"
      };
      return h(
        ElTag,
        {
          type: typeColors[row.contentType] || "info",
          size: "small"
        },
        () => `${typeIcons[row.contentType] || ""} ${row.contentType?.toUpperCase() || "TEXT"}`
      );
    }
  },
  {
    prop: "content",
    align: "left",
    minWidth: 300,
    headerRenderer: () => $t("Content"),
    cellRenderer: ({ row }) => {
      const content = row.content || "";
      const truncated = content.length > 100 ? content.substring(0, 100) + "..." : content;
      
      return h("div", { class: "flex flex-col" }, [
        h("div", { 
          class: "text-sm",
          style: { wordBreak: "break-word" }
        }, truncated),
        row.attachments && row.attachments.length > 0 && 
        h("div", { class: "text-xs text-blue-500 mt-1" }, 
          `📎 ${row.attachments.length} attachment(s)`
        ),
        row.isEdited && 
        h("span", { class: "text-xs text-gray-400 mt-1" }, 
          `✏️ Edited ${dayjs(row.editedAt).format("MM-DD HH:mm")}`
        )
      ]);
    }
  },
  {
    prop: "parentMessage",
    align: "left",
    width: 120,
    headerRenderer: () => $t("Reply To"),
    cellRenderer: ({ row }) => {
      if (!row.parentMessage) return "-";
      const parentContent = row.parentMessage.content || "";
      const truncated = parentContent.length > 30 ? parentContent.substring(0, 30) + "..." : parentContent;
      return h(ElTooltip, {
        content: parentContent,
        placement: "top"
      }, {
        default: () => h("div", { class: "text-xs text-blue-500 cursor-pointer" }, 
          `↳ ${truncated}`
        )
      });
    }
  },
  {
    prop: "createdAt",
    align: "center",
    width: 140,
    headerRenderer: () => $t("Created At"),
    cellRenderer: ({ row }) =>
      dayjs(row.createdAt).format("YYYY-MM-DD HH:mm")
  },
  {
    prop: "operation",
    fixed: "right",
    width: 100,
    slot: "operation",
    headerRenderer: () => $t("Operation")
  }
];
