import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const modelToolRules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: $t("Please enter the tool name"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 100,
      message: $t("Length must be between 2 and 100 characters"),
      trigger: "blur"
    }
  ],
  slug: [
    {
      required: true,
      message: $t("Please enter the tool slug"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 100,
      message: $t("Length must be between 2 and 100 characters"),
      trigger: "blur"
    },
    {
      pattern: /^[a-z0-9-_]+$/,
      message: $t(
        "Slug can only contain lowercase letters, numbers, hyphens and underscores"
      ),
      trigger: "blur"
    }
  ],
  toolType: [
    {
      required: true,
      message: $t("Please select tool type"),
      trigger: "change"
    }
  ]
});

export { modelToolRules };
