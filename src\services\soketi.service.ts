// src/services/soketi.service.ts
import { computed, reactive } from "vue";
import Pusher, { type Channel } from "pusher-js";
import { ElMessage } from "element-plus";
import { $t } from "@/plugins/i18n";
import { getToken } from "@/utils/auth";

// ===== INTERFACES & TYPES =====
interface SoketiConfig {
  appKey: string;
  host: string;
  port: number;
  useTLS: boolean;
  apiUrl: string;
  cluster?: string;
  enabledTransports?: string[];
  maxReconnectAttempts?: number;
  baseReconnectDelay?: number;
  maxReconnectDelay?: number;
}

interface ConnectionState {
  connected: boolean;
  connecting: boolean;
  reconnecting: boolean;
  error: string | null;
  lastConnected: Date | null;
  reconnectAttempts: number;
  connectionId: string | null;
}

interface ChannelSubscription {
  channel: Channel;
  events: Map<string, Set<EventCallback>>;
  subscribed: boolean;
  lastActivity: Date;
}

interface EventCallback<T = any> {
  (data: T): void;
}

interface BroadcastOptions {
  retryOnFail?: boolean;
  timeout?: number;
}

// ===== ENUMS =====
enum ConnectionStates {
  CONNECTING = "connecting",
  CONNECTED = "connected",
  DISCONNECTED = "disconnected",
  UNAVAILABLE = "unavailable"
}

enum ChannelTypes {
  PUBLIC = "public",
  PRIVATE = "private",
  PRESENCE = "presence"
}

// ===== UTILITY FUNCTIONS =====
class ConfigValidator {
  private static readonly REQUIRED_ENV_VARS = [
    "VITE_PUSHER_APP_KEY",
    "VITE_PUSHER_HOST",
    "VITE_PUSHER_PORT",
    "VITE_PUSHER_APP_CLUSTER",
    "VITE_PUSHER_SCHEME",
    "VITE_API_BASE_URL"
  ] as const;

  static validate(): SoketiConfig | null {
    const missing = this.REQUIRED_ENV_VARS.filter(key => !import.meta.env[key]);

    if (missing.length > 0) {
      console.error(
        `[Soketi] Missing required environment variables: ${missing.join(", ")}`
      );
      return null;
    }

    const port = Number(import.meta.env.VITE_PUSHER_PORT);
    if (isNaN(port) || port <= 0) {
      console.error(
        `[Soketi] Invalid port number: ${import.meta.env.VITE_PUSHER_PORT}`
      );
      return null;
    }

    return {
      appKey: import.meta.env.VITE_PUSHER_APP_KEY,
      host: import.meta.env.VITE_PUSHER_HOST,
      port,
      useTLS: import.meta.env.VITE_PUSHER_USE_TLS === "true",
      apiUrl: import.meta.env.VITE_API_BASE_URL.replace(/\/$/, ""),
      cluster: import.meta.env.VITE_PUSHER_CLUSTER || "mt1",
      enabledTransports: ["ws", "wss"],
      maxReconnectAttempts: 10,
      baseReconnectDelay: 1000,
      maxReconnectDelay: 30000
    };
  }

  static getChannelType(channelName: string): ChannelTypes {
    if (channelName.startsWith("private-")) return ChannelTypes.PRIVATE;
    if (channelName.startsWith("presence-")) return ChannelTypes.PRESENCE;
    return ChannelTypes.PUBLIC;
  }
}

class Logger {
  private static readonly PREFIX = "[Soketi]";

  static info(message: string, ...args: any[]): void {
    console.log(`${this.PREFIX} ${message}`, ...args);
  }

  static warn(message: string, ...args: any[]): void {
    console.warn(`${this.PREFIX} ${message}`, ...args);
  }

  static error(message: string, ...args: any[]): void {
    console.error(`${this.PREFIX} ${message}`, ...args);
  }

  static debug(message: string, ...args: any[]): void {
    console.debug(`${this.PREFIX} ${message}`, ...args);
  }
}

// ===== MAIN SERVICE =====
class SoketiService {
  private static instance: SoketiService;
  private pusher: Pusher | null = null;
  private channels: Map<string, ChannelSubscription> = new Map();
  private readonly config: SoketiConfig | null = null;

  // Reconnection management
  private reconnectTimer: number | null = null;
  private connectionPromise: Promise<boolean> | null = null;

  // Cleanup tracking
  private eventListeners: Array<() => void> = [];

  // Reactive state
  public readonly state = reactive<ConnectionState>({
    connected: false,
    connecting: false,
    reconnecting: false,
    error: null,
    lastConnected: null,
    reconnectAttempts: 0,
    connectionId: null
  });

  // Computed properties
  public readonly isReady = computed(
    () => this.state.connected && !this.state.connecting
  );

  public readonly connectionStatus = computed(() => {
    if (this.state.connecting) return "Đang kết nối...";
    if (this.state.connected) return "Đã kết nối";
    if (this.state.reconnecting) return "Đang kết nối lại...";
    return "Đã ngắt kết nối";
  });

  // Singleton pattern
  public static getInstance(): SoketiService {
    if (!SoketiService.instance) {
      SoketiService.instance = new SoketiService();
    }
    return SoketiService.instance;
  }

  private constructor() {
    this.config = ConfigValidator.validate();
    if (this.config) {
      this.setupWindowEventHandlers();
      Logger.info("Service initialized with config:", {
        host: this.config.host,
        port: this.config.port,
        useTLS: this.config.useTLS
      });
    } else {
      Logger.error("Failed to initialize: Invalid configuration");
    }
  }

  // ===== CONNECTION MANAGEMENT =====
  public async connect(): Promise<boolean> {
    // Return existing connection promise if already connecting
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    // Return true if already connected
    if (this.pusher?.connection.state === ConnectionStates.CONNECTED) {
      return true;
    }

    if (!this.config) {
      Logger.error("Cannot connect: Invalid configuration");
      return false;
    }

    this.connectionPromise = this.initializeConnection();

    try {
      return await this.connectionPromise;
    } finally {
      this.connectionPromise = null;
    }
  }

  private async initializeConnection(): Promise<boolean> {
    return new Promise(resolve => {
      if (!this.config) {
        resolve(false);
        return;
      }

      Logger.info("Initializing connection...");
      this.updateState({
        connecting: true,
        error: null,
        reconnecting: this.state.reconnectAttempts > 0
      });

      try {
        this.pusher = new Pusher(this.config.appKey, {
          wsHost: this.config.host,
          wsPort: this.config.port,
          wssPort: this.config.port,
          forceTLS: this.config.useTLS,
          enabledTransports: ["ws", "wss"],
          disableStats: true,
          cluster: this.config.cluster,

          // Authorization for private/presence channels
          authorizer: channel => ({
            authorize: (socketId, authCallback) => {
              this.authorizeChannel(socketId, channel.name, authCallback);
            }
          })
        });

        this.setupConnectionEvents(resolve);
      } catch (error: any) {
        Logger.error("Failed to initialize Pusher:", error);
        this.handleConnectionError(error.message || "Initialization failed");
        resolve(false);
      }
    });
  }

  private async authorizeChannel(
    socketId: string,
    channelName: string,
    callback: (error: any, data: any) => void
  ): Promise<void> {
    if (!this.config) {
      callback(new Error("No configuration available"), null);
      return;
    }

    const channelType = ConfigValidator.getChannelType(channelName);

    // Public channels don't need authorization
    if (channelType === ChannelTypes.PUBLIC) {
      callback(null, {});
      return;
    }

    try {
      Logger.debug(`Authorizing ${channelType} channel: ${channelName}`);

      const token = getToken().accessToken ?? getToken();
      if (!token) {
        throw new Error("Authentication token not available");
      }

      console.log("Authorizing token:------------->", this.config);

      const response = await fetch(`${this.config.apiUrl}/broadcasting/auth`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${token}`,
          "X-Requested-With": "XMLHttpRequest"
        },
        body: JSON.stringify({
          socket_id: socketId,
          channel_name: channelName
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      Logger.debug(`Authorization successful for ${channelName}`);
      callback(null, data);
    } catch (error: any) {
      Logger.error(`Authorization failed for ${channelName}:`, error);

      // User-friendly error messages
      if (error.message.includes("401")) {
        ElMessage.error($t("Authentication required"));
      } else if (error.message.includes("403")) {
        ElMessage.error(
          $t("Permission denied for channel: {channel}", {
            channel: channelName
          })
        );
      } else if (
        error.message.includes("NetworkError") ||
        error.message.includes("fetch")
      ) {
        ElMessage.error($t("Network error during authorization"));
      } else {
        ElMessage.error($t("Authorization failed"));
      }

      callback(error, null);
    }
  }

  private setupConnectionEvents(callback: (success: boolean) => void): void {
    if (!this.pusher) return;

    let callbackInvoked = false;
    let invokeCallback = (success: boolean) => {
      if (!callbackInvoked) {
        callbackInvoked = true;
        callback(success);
      }
    };

    // Connection state handlers
    this.pusher.connection.bind("connecting", () => {
      Logger.debug("Connection state: connecting");
      this.updateState({ connecting: true });
    });

    this.pusher.connection.bind("connected", () => {
      Logger.info("Connected successfully");
      this.updateState({
        connected: true,
        connecting: false,
        reconnecting: false,
        error: null,
        lastConnected: new Date(),
        reconnectAttempts: 0,
        connectionId: this.pusher?.connection.socket_id || null
      });

      this.clearReconnectTimer();
      this.resubscribeChannels();
      invokeCallback(true);
    });

    this.pusher.connection.bind("disconnected", () => {
      Logger.warn("Disconnected from server");
      this.updateState({
        connected: false,
        connecting: false,
        connectionId: null
      });

      if (!this.state.reconnecting) {
        this.scheduleReconnection();
      }
    });

    this.pusher.connection.bind("error", (err: any) => {
      Logger.error("Connection error:", err);
      this.handleConnectionError(err.message || "Connection error");
      invokeCallback(false);
    });

    this.pusher.connection.bind("unavailable", () => {
      Logger.error("Connection unavailable");
      this.handleConnectionError("Connection unavailable");
      invokeCallback(false);
    });

    // Connection timeout protection
    const timeoutId = setTimeout(() => {
      if (!callbackInvoked && this.state.connecting) {
        Logger.error("Connection timeout");
        this.handleConnectionError("Connection timeout");
        invokeCallback(false);
      }
    }, 15000);

    // Clear timeout when callback is invoked
    const originalCallback = invokeCallback;
    invokeCallback = (success: boolean) => {
      clearTimeout(timeoutId);
      originalCallback(success);
    };
  }

  private resubscribeChannels(): void {
    if (this.channels.size === 0) return;

    Logger.info(`Resubscribing to ${this.channels.size} channels`);

    this.channels.forEach((subscription, channelName) => {
      if (this.pusher) {
        // Unsubscribe old channel
        this.pusher.unsubscribe(channelName);

        // Create new subscription
        const newChannel = this.pusher.subscribe(channelName);
        subscription.channel = newChannel;
        subscription.subscribed = false;
        subscription.lastActivity = new Date();

        // Setup events cho channel mới
        this.setupChannelEvents(newChannel, channelName, subscription);

        // Re-bind tất cả events
        subscription.events.forEach((callbacks, eventName) => {
          newChannel.bind(eventName, (data: any) => {
            subscription.lastActivity = new Date();
            callbacks.forEach(cb => {
              try {
                cb(data);
              } catch (e) {
                Logger.error("Event callback error:", e);
              }
            });
          });
        });
      }
    });
  }

  private scheduleReconnection(): void {
    if (
      !this.config ||
      this.state.reconnectAttempts >= this.config.maxReconnectAttempts!
    ) {
      Logger.error("Max reconnection attempts reached");
      return;
    }

    this.clearReconnectTimer();

    const delay = Math.min(
      this.config.baseReconnectDelay! *
        Math.pow(2, this.state.reconnectAttempts),
      this.config.maxReconnectDelay!
    );

    Logger.info(
      `Scheduling reconnection in ${delay}ms (attempt ${this.state.reconnectAttempts + 1})`
    );

    this.reconnectTimer = window.setTimeout(() => {
      this.updateState({ reconnectAttempts: this.state.reconnectAttempts + 1 });
      this.connect().catch();
    }, delay);
  }

  public disconnect(): void {
    Logger.info("Disconnecting...");

    this.clearReconnectTimer();
    this.unsubscribeAllChannels();

    if (this.pusher) {
      this.pusher.disconnect();
      this.pusher = null;
    }

    this.updateState({
      connected: false,
      connecting: false,
      reconnecting: false,
      reconnectAttempts: 0,
      connectionId: null
    });
  }

  // ===== CHANNEL MANAGEMENT =====
  public subscribe<T = any>(
    channelName: string,
    eventName: string,
    callback: EventCallback<T>
  ): boolean {
    if (!this.pusher) {
      Logger.warn("Cannot subscribe: Not connected");
      return false;
    }

    if (!channelName || !eventName || !callback) {
      Logger.error("Invalid subscription parameters");
      return false;
    }

    let subscription = this.channels.get(channelName);

    if (!subscription) {
      Logger.debug(`Creating new subscription for channel: ${channelName}`);

      const channel = this.pusher.subscribe(channelName);
      subscription = {
        channel,
        events: new Map(),
        subscribed: false,
        lastActivity: new Date()
      };

      this.channels.set(channelName, subscription);
      this.setupChannelEvents(channel, channelName, subscription);
    }

    // Normalize event name
    const normalizedEventName = eventName.replace(/^\./, "");

    // Initialize event callbacks set if not exists
    if (!subscription.events.has(normalizedEventName)) {
      subscription.events.set(normalizedEventName, new Set());
    }

    const callbacks = subscription.events.get(normalizedEventName)!;
    callbacks.add(callback);

    // Bind event handler only once per event
    if (callbacks.size === 1) {
      subscription.channel.bind(normalizedEventName, (data: T) => {
        subscription!.lastActivity = new Date();
        callbacks.forEach(cb => {
          try {
            cb(data);
          } catch (error) {
            Logger.error(
              `Event callback error for ${normalizedEventName}:`,
              error
            );
          }
        });
      });

      Logger.debug(
        `Bound event handler for ${normalizedEventName} on ${channelName}`
      );
    }

    return true;
  }

  private setupChannelEvents(
    channel: Channel,
    channelName: string,
    subscription: ChannelSubscription
  ): void {
    channel.bind("pusher:subscription_succeeded", () => {
      subscription.subscribed = true;
      Logger.debug(`Successfully subscribed to ${channelName}`);
    });

    channel.bind("pusher:subscription_error", (error: any) => {
      Logger.error(`Subscription failed for ${channelName}:`, error);

      // Remove failed subscription
      this.channels.delete(channelName);

      // Show user-friendly error
      const channelType = ConfigValidator.getChannelType(channelName);
      if (channelType !== ChannelTypes.PUBLIC) {
        ElMessage.error(
          $t("Failed to subscribe to {channel}", { channel: channelName })
        );
      }
    });
  }

  public broadcast<T = any>(
    channelName: string,
    eventName: string,
    data: T,
    options: BroadcastOptions = {}
  ): boolean {
    if (!this.pusher || !this.state.connected) {
      Logger.warn("Cannot broadcast: Not connected");
      return false;
    }

    const subscription = this.channels.get(channelName);
    if (!subscription || !subscription.subscribed) {
      Logger.warn(`Cannot broadcast: Not subscribed to ${channelName}`);
      return false;
    }

    try {
      const clientEventName = eventName.startsWith("client-")
        ? eventName
        : `client-${eventName}`;

      subscription.channel.trigger(clientEventName, data);
      subscription.lastActivity = new Date();

      Logger.debug(`Broadcast sent to ${channelName}:`, {
        event: clientEventName,
        data
      });
      return true;
    } catch (error: any) {
      Logger.error(`Broadcast failed for ${channelName}:`, error);

      if (options.retryOnFail && this.state.connected) {
        Logger.info("Retrying broadcast...");
        setTimeout(
          () =>
            this.broadcast(channelName, eventName, data, {
              ...options,
              retryOnFail: false
            }),
          1000
        );
      }

      return false;
    }
  }

  public unsubscribe(
    channelName: string,
    eventName?: string,
    callback?: EventCallback
  ): void {
    const subscription = this.channels.get(channelName);
    if (!subscription) {
      Logger.warn(`Cannot unsubscribe: Channel ${channelName} not found`);
      return;
    }

    if (eventName) {
      const normalizedEventName = eventName.replace(/^\./, "");
      const callbacks = subscription.events.get(normalizedEventName);

      if (callbacks) {
        if (callback) {
          callbacks.delete(callback);
          Logger.debug(
            `Removed specific callback for ${normalizedEventName} on ${channelName}`
          );

          if (callbacks.size === 0) {
            subscription.channel.unbind(normalizedEventName);
            subscription.events.delete(normalizedEventName);
            Logger.debug(
              `Unbound event ${normalizedEventName} from ${channelName}`
            );
          }
        } else {
          subscription.channel.unbind(normalizedEventName);
          subscription.events.delete(normalizedEventName);
          Logger.debug(
            `Unbound all callbacks for ${normalizedEventName} on ${channelName}`
          );
        }
      }
    } else {
      this.unsubscribeChannel(channelName);
    }
  }

  private unsubscribeChannel(channelName: string): void {
    const subscription = this.channels.get(channelName);
    if (!subscription) return;

    Logger.debug(`Unsubscribing from channel: ${channelName}`);

    // Unbind all events
    subscription.events.forEach((_, eventName) => {
      subscription.channel.unbind(eventName);
    });

    // Unsubscribe from pusher
    if (this.pusher) {
      this.pusher.unsubscribe(channelName);
    }

    // Remove from local tracking
    this.channels.delete(channelName);
  }

  private unsubscribeAllChannels(): void {
    Logger.info(`Unsubscribing from all ${this.channels.size} channels`);

    this.channels.forEach((_, channelName) => {
      this.unsubscribeChannel(channelName);
    });
  }

  // ===== UTILITY METHODS =====
  private updateState(updates: Partial<ConnectionState>): void {
    Object.assign(this.state, updates);
  }

  private handleConnectionError(message: string): void {
    this.updateState({
      error: message,
      connected: false,
      connecting: false
    });

    Logger.error("Connection error:", message);
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private setupWindowEventHandlers(): void {
    // Handle page unload
    const unloadHandler = () => {
      Logger.debug("Page unloading, disconnecting...");
      this.disconnect();
    };

    // Handle visibility changes
    const visibilityHandler = () => {
      if (document.hidden) {
        Logger.debug("Page hidden, pausing reconnection");
        this.clearReconnectTimer();
      } else if (!this.state.connected && !this.state.connecting) {
        Logger.debug("Page visible, resuming connection");
        this.scheduleReconnection();
      }
    };

    // Handle network status changes
    const onlineHandler = () => {
      Logger.info("Network online, attempting reconnection");
      if (!this.state.connected && !this.state.connecting) {
        this.connect();
      }
    };

    const offlineHandler = () => {
      Logger.warn("Network offline");
      this.clearReconnectTimer();
    };

    // Register event listeners
    window.addEventListener("beforeunload", unloadHandler);
    document.addEventListener("visibilitychange", visibilityHandler);
    window.addEventListener("online", onlineHandler);
    window.addEventListener("offline", offlineHandler);

    // Store cleanup functions
    this.eventListeners.push(
      () => window.removeEventListener("beforeunload", unloadHandler),
      () => document.removeEventListener("visibilitychange", visibilityHandler),
      () => window.removeEventListener("online", onlineHandler),
      () => window.removeEventListener("offline", offlineHandler)
    );
  }

  // ===== PUBLIC UTILITY METHODS =====
  public getChannelInfo(channelName: string) {
    const subscription = this.channels.get(channelName);
    if (!subscription) return null;

    return {
      name: channelName,
      type: ConfigValidator.getChannelType(channelName),
      subscribed: subscription.subscribed,
      eventCount: subscription.events.size,
      lastActivity: subscription.lastActivity,
      events: Array.from(subscription.events.keys())
    };
  }

  public getAllChannelsInfo() {
    return Array.from(this.channels.keys())
      .map(name => this.getChannelInfo(name))
      .filter(Boolean);
  }

  public getConnectionInfo() {
    return {
      ...this.state,
      channelCount: this.channels.size,
      pusherState: this.pusher?.connection.state || "not_initialized",
      config: this.config
        ? {
            host: this.config.host,
            port: this.config.port,
            useTLS: this.config.useTLS,
            cluster: this.config.cluster
          }
        : null
    };
  }

  public getChannelNames(): string[] {
    return Array.from(this.channels.keys());
  }

  public destroy(): void {
    Logger.info("Destroying service...");

    this.disconnect();

    // Clean up event listeners
    this.eventListeners.forEach(cleanup => cleanup());
    this.eventListeners = [];

    // Clear singleton instance
    SoketiService.instance = null as any;
  }
}

// ===== COMPOSABLE FUNCTION =====
export function useSoketi() {
  const service = SoketiService.getInstance();

  return {
    // State
    state: service.state,
    isReady: service.isReady,
    connectionStatus: service.connectionStatus,

    // Connection methods
    connect: () => service.connect(),
    disconnect: () => service.disconnect(),

    // Channel methods
    subscribe: service.subscribe.bind(service),
    unsubscribe: service.unsubscribe.bind(service),
    broadcast: service.broadcast.bind(service),

    // Utility methods
    isConnected: () => service.state.connected,
    getChannels: () => service.getChannelNames(),
    getChannelInfo: service.getChannelInfo.bind(service),
    getAllChannelsInfo: service.getAllChannelsInfo.bind(service),
    getConnectionInfo: service.getConnectionInfo.bind(service)
  };
}

// ===== EXPORTS =====
export const soketiService = SoketiService.getInstance();

// Auto cleanup on page unload
if (typeof window !== "undefined") {
  window.addEventListener("beforeunload", () => {
    soketiService.destroy();
  });
}
