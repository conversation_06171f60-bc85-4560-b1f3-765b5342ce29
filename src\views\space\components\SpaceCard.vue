<script setup lang="ts">
import { useRouter } from "vue-router";

interface Props {
  space?: {
    id?: number;
    uuid?: string;
    name?: string;
    description?: string;
    email?: string;
    phone?: string;
    logo?: string;
    createdAt?: string;
    membersCount?: number;
    botsCount?: number;
    status?: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  space: () => ({
    id: 1,
    uuid: "tech-company",
    name: "Tech Company",
    description: "Công ty công nghệ hàng đầu",
    email: "<EMAIL>",
    phone: "+84 ***********",
    logo: "",
    createdAt: "2020-03-15",
    membersCount: 124,
    botsCount: 15,
    status: "active"
  })
});

const router = useRouter();

const handleViewDetails = () => {
  if (props.space?.uuid) {
    router.push({
      name: "SpaceDetail",
      params: { slug: props.space.slug }
    });
  }
};

const handleEdit = () => {
  // Emit event để parent component xử lý
  emit("edit", props.space);
};

const handleDelete = () => {
  // Emit event để parent component xử lý
  emit("delete", props.space.uuid);
};

const emit = defineEmits<{
  edit: [space: any];
  delete: [space: any];
}>();

// Format date
const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  return date.toLocaleDateString("vi-VN");
};

// Get avatar initials
const getAvatarInitials = (name: string) => {
  if (!name) return "TC";
  return name
    .split(" ")
    .map(word => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};
</script>

<template>
  <div class="w-full max-w-sm rounded-2xl shadow-lg overflow-hidden bg-white">
    <div class="relative h-28 bg-gradient-to-br from-indigo-500 to-purple-600">
      <!-- MODIFIED: Thêm popper-class để áp dụng style tùy chỉnh -->
      <el-dropdown
        class="!absolute top-3 right-3"
        trigger="click"
        popper-class="wide-dropdown-popper"
      >
        <span
          class="p-1 rounded-full text-white hover:bg-white/20 transition-colors cursor-pointer focus:outline-none"
        >
          <IconifyIconOnline
            :icon="'ant-design:more-outlined'"
            class="text-2xl text-white"
          />
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleEdit">
              <IconifyIconOnline
                :icon="'tabler:edit'"
                class="mr-2 text-blue-600"
              />
              Sửa
            </el-dropdown-item>
            <el-dropdown-item @click="handleDelete">
              <IconifyIconOnline
                :icon="'tabler:trash-x-filled'"
                class="mr-2 text-red-700"
              />
              Xóa
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-avatar
        :size="80"
        :src="space?.logo"
        class="absolute -bottom-10 left-6 border-4 border-white bg-purple-800 text-white font-bold text-2xl"
      >
        {{ getAvatarInitials(space?.name || "") }}
      </el-avatar>
    </div>
    <div class="p-6">
      <div class="mt-6">
        <h2 class="text-2xl font-bold text-gray-800">
          {{ space?.name || "Unnamed Space" }}
        </h2>
      </div>

      <div class="mt-3 space-y-2 text-gray-600">
        <div class="flex items-center">
          <IconifyIconOnline
            :icon="'tabler:calendar'"
            class="mr-3 text-blue-600"
          />
          <span class="text-sm"
            >Thành lập: {{ formatDate(space?.createdAt || "") }}</span
          >
        </div>
        <div class="flex items-center">
          <IconifyIconOnline :icon="'fa:group'" class="mr-3 text-green-600" />
          <span class="text-sm"
            >Thành viên: {{ space?.membersCount || 0 }} người</span
          >
        </div>
        <div class="flex items-center">
          <IconifyIconOnline
            :icon="'mdi:monitor'"
            class="mr-3 text-[#a855f7]"
          />
          <span class="text-sm"
            >Bot: {{ space?.botsCount || 0 }} AI assistants</span
          >
        </div>
      </div>

      <div class="mt-6 flex items-center justify-between">
        <div
          :class="[
            'inline-flex items-center rounded-full py-1 px-3 text-sm font-semibold',
            space?.status === 'active'
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          ]"
        >
          <span
            :class="[
              'w-2 h-2 rounded-full mr-2',
              space?.status === 'active' ? 'bg-green-500' : 'bg-gray-500'
            ]"
          />
          {{
            space?.status === "active" ? "Đang hoạt động" : "Không hoạt động"
          }}
        </div>

        <el-link
          type="primary"
          underline="never"
          class="font-semibold text-sm cursor-pointer"
          @click="handleViewDetails"
        >
          Xem chi tiết →
        </el-link>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
