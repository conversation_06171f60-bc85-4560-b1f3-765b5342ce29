<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { PlusDialogForm } from "plus-pro-components";
import { dropdownLanguages } from "@/views/language/utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";

interface Props {
  visible: boolean;
  values: FieldValues;
  groups: any[];
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "search", values: FieldValues): void;
  (e: "reset"): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  values: () => ({}),
  groups: () => []
});

const emit = defineEmits<Emits>();

const formRef = ref();
const languages = ref([]);

onMounted(async () => {
  try {
    const { data } = await dropdownLanguages();
    languages.value = useConvertKeyToCamel(data);
  } catch (error) {
    console.error("Failed to load languages:", error);
  }
});

const groupOptions = computed(() => {
  const allOption = { label: $t("All"), value: "" };
  const groupOpts = props.groups.map(group => ({
    label: group.name || group,
    value: group.name || group
  }));
  return [allOption, ...groupOpts];
});

const languageOptions = computed(() => {
  const allOption = { label: $t("All"), value: "" };
  const langOpts = languages.value.map((lang: any) => ({
    label: `${lang.name} (${lang.code})`,
    value: lang.code
  }));
  return [allOption, ...langOpts];
});

const columns = [
  {
    label: computed(() => $t("Translation Key")),
    prop: "key",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter translation key")
    }
  },
  {
    label: computed(() => $t("Language")),
    prop: "languageCode",
    valueType: "select",
    options: languageOptions,
    fieldProps: {
      placeholder: $t("Select language"),
      filterable: true
    }
  },
  {
    label: computed(() => $t("Translation Value")),
    prop: "value",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter translation value")
    }
  },
  {
    label: computed(() => $t("Group")),
    prop: "group",
    valueType: "select",
    options: groupOptions,
    fieldProps: {
      placeholder: $t("Select group"),
      filterable: true
    }
  },
  {
    label: computed(() => $t("Is Plural")),
    prop: "isPlural",
    valueType: "select",
    options: [
      { label: $t("All"), value: "" },
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ],
    fieldProps: {
      placeholder: $t("Select plural status")
    }
  },
  {
    label: computed(() => $t("Is Active")),
    prop: "isActive",
    valueType: "select",
    options: [
      { label: $t("All"), value: "" },
      { label: $t("Active"), value: true },
      { label: $t("Inactive"), value: false }
    ],
    fieldProps: {
      placeholder: $t("Select active status")
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    options: [
      { label: $t("All"), value: "" },
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ],
    fieldProps: {
      placeholder: $t("Select status")
    }
  },
  {
    label: computed(() => $t("Trashed")),
    prop: "isTrashed",
    valueType: "select",
    options: [
      { label: $t("All"), value: "" },
      { label: $t("Yes"), value: "yes" },
      { label: $t("No"), value: "no" }
    ],
    fieldProps: {
      placeholder: $t("Select trashed status")
    }
  }
];

const handleSearch = (values: FieldValues) => {
  emit("search", values);
};

const handleReset = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
  emit("reset");
};
</script>

<template>
  <PlusDialogForm
    ref="formRef"
    v-model:visible="visible"
    v-model="values"
    :form="{
      columns,
      labelWidth: '140px',
      labelPosition: 'right',
      hasFooter: true
    }"
    :dialog="{
      title: $t('Filter Translations'),
      width: '700px',
      top: '5vh',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      draggable: true
    }"
    :footerButtons="[
      {
        label: $t('Reset'),
        type: 'default',
        onClick: handleReset
      },
      {
        label: $t('Search'),
        type: 'primary',
        onClick: handleSearch
      }
    ]"
    @update:visible="emit('update:visible', $event)"
  />
</template>
