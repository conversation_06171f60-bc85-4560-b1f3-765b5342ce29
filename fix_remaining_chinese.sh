#!/bin/bash

echo "🔧 Fixing remaining Chinese characters in utils and other directories..."

# Function to replace Chinese text in a specific file
fix_file() {
    local file="$1"
    echo "Processing: $file"
    
    # Create backup
    cp "$file" "$file.bak2"
    
    # More comprehensive Chinese replacements
    sed -i 's/是否为/Whether it is/g' "$file"
    sed -i 's/判断/Check/g' "$file"
    sed -i 's/停止/Stop/g' "$file"
    sed -i 's/鼠标移入/Mouse enter/g' "$file"
    sed -i 's/Event处理/Event handling/g' "$file"
    sed -i 's/将/Convert/g' "$file"
    sed -i 's/Menu树形结构/Menu tree structure/g' "$file"
    sed -i 's/扁平化为/Flatten to/g' "$file"
    sed -i 's/一维数组/One-dimensional array/g' "$file"
    sed -i 's/用于/Used for/g' "$file"
    sed -i 's/查询/Query/g' "$file"
    sed -i 's/延时处理/Delayed processing/g' "$file"
    sed -i 's/防止/Prevent/g' "$file"
    sed -i 's/User看到/User seeing/g' "$file"
    sed -i 's/某些Actions/Some actions/g' "$file"
    sed -i 's/紧凑页面/Compact page/g' "$file"
    sed -i 's/轻松找到/Easily find/g' "$file"
    sed -i 's/所需Info/Required info/g' "$file"
    sed -i 's/同步时光/Sync time/g' "$file"
    sed -i 's/界面随晨昏/Interface follows dawn and dusk/g' "$file"
    sed -i 's/自然呼应/Natural response/g' "$file"
    sed -i 's/接收/Receive/g' "$file"
    sed -i 's/Sidebar切换/Sidebar switching/g' "$file"
    sed -i 's/传递过来的/Passed/g' "$file"
    sed -i 's/Parameter/Parameter/g' "$file"
    sed -i 's/redirect时/During redirect/g' "$file"
    sed -i 's/置换成任意值/Replace with any value/g' "$file"
    sed -i 's/待重定向后/After redirection/g' "$file"
    sed -i 's/重新赋值/Reassign value/g' "$file"
    sed -i 's/Abnormal页面/Abnormal page/g' "$file"
    sed -i 's/这里的layout/The layout here/g' "$file"
    sed -i 's/监听/Listen to/g' "$file"
    sed -i 's/Container拖拉后/After container dragging/g' "$file"
    sed -i 's/恢复对应的/Restore corresponding/g' "$file"
    sed -i 's/Navigation模式/Navigation mode/g' "$file"
    sed -i 's/如果title为空/If title is empty/g' "$file"
    sed -i 's/拒绝Add空Info到Tag页/Refuse to add empty info to tag page/g' "$file"
    sed -i 's/Cache页面/Cache page/g' "$file"
    sed -i 's/keepAlive/keepAlive/g' "$file"
    sed -i 's/Does not exist则Delete/Delete if does not exist/g' "$file"
    sed -i 's/ClearCache页面/Clear cache page/g' "$file"
    sed -i 's/无感Refresh/Seamless refresh/g' "$file"
    sed -i 's/后端Back/Backend returns/g' "$file"
    sed -i 's/访问Interface使用的/Used for accessing interfaces/g' "$file"
    sed -i 's/Refresh/Refresh/g' "$file"
    sed -i 's/Interface时所需的/Required for interface/g' "$file"
    sed -i 's/过期Time/Expiration time/g' "$file"
    sed -i 's/比如30天/For example 30 days/g' "$file"
    sed -i 's/应大于/Should be greater than/g' "$file"
    sed -i 's/比如2小时/For example 2 hours/g' "$file"
    sed -i 's/阻止通过键盘/Prevent through keyboard/g' "$file"
    sed -i 's/快捷键Open/Shortcut key to open/g' "$file"
    sed -i 's/浏览器开发者ToolPanel/Browser developer tool panel/g' "$file"
    sed -i 's/阻止页面元素Selected/Prevent page element selection/g' "$file"
    sed -i 's/从 vue-types v5.0 开始/Starting from vue-types v5.0/g' "$file"
    sed -i 's/extend()Method已经废弃/extend() method has been deprecated/g' "$file"
    sed -i 's/当前已改为官方推荐的ES6+Method/Currently changed to officially recommended ES6+ method/g' "$file"
    sed -i 's/简版前端单点Login/Simple frontend single sign-on login/g' "$file"
    sed -i 's/根据实际业务自Row编写/Write according to actual business needs/g' "$file"
    sed -i 's/平台启动后本地可以跳后面这个Link进RowTest/After platform startup, you can jump to this link for local testing/g' "$file"
    sed -i 's/获取url中的重要ParameterInfo/Get important parameter info from url/g' "$file"
    sed -i 's/然后通过 setToken Save在本地/Then save locally through setToken/g' "$file"
    sed -i 's/使用 window.location.replace 跳转正确页面/Use window.location.replace to jump to correct page/g' "$file"
    sed -i 's/创建层级关系/Create hierarchical relationship/g' "$file"
    sed -i 's/创建层级关系后的树/Tree after creating hierarchical relationship/g' "$file"
    sed -i 's/抱歉，你无权访问该页面/Sorry, you do not have permission to access this page/g' "$file"
    sed -i 's/抱歉，你访问的页面Does not exist/Sorry, the page you are visiting does not exist/g' "$file"
    sed -i 's/提取Menu树中的每一项uniqueId/Extract uniqueId of each item in Menu tree/g' "$file"
    sed -i 's/Response式storage/Responsive storage/g' "$file"
    sed -i 's/按需引入element-plus/Import element-plus on demand/g' "$file"
    sed -i 's/该Method稳定且明确/This method is stable and clear/g' "$file"
    sed -i 's/当然也支持/Of course also supports/g' "$file"
    sed -i 's/快速开始/Quick start/g' "$file"
    sed -i 's/按需导入/Import on demand/g' "$file"
    
    echo "Completed: $file"
}

# Process specific files that still contain Chinese
files_to_fix=(
    "src/utils/propTypes.ts"
    "src/utils/sso.ts"
    "src/utils/tree.ts"
    "src/utils/responsive.ts"
    "src/views/error/403.vue"
    "src/views/error/404.vue"
    "src/layout/components/lay-notice/components/NoticeItem.vue"
    "src/layout/components/lay-search/components/SearchHistory.vue"
    "src/layout/components/lay-search/components/SearchModal.vue"
    "src/layout/components/lay-setting/index.vue"
    "src/layout/components/lay-tag/index.vue"
    "src/layout/frame.vue"
    "src/router/modules/error.ts"
    "src/store/modules/app.ts"
    "src/store/modules/multiTags.ts"
    "src/store/modules/permission.ts"
    "src/plugins/elementPlus.ts"
)

for file in "${files_to_fix[@]}"; do
    if [ -f "$file" ]; then
        fix_file "$file"
    else
        echo "File not found: $file"
    fi
done

echo ""
echo "✅ Completed fixing remaining Chinese characters!"
echo "📁 Backup files created with .bak2 extension"
echo ""
echo "🔍 Running final verification..."

# Count remaining Chinese characters
remaining=0
for file in "${files_to_fix[@]}"; do
    if [ -f "$file" ]; then
        if grep -q "[\u4e00-\u9fff]" "$file" 2>/dev/null; then
            remaining=$((remaining + 1))
            echo "❌ Still has Chinese: $file"
        else
            echo "✅ Clean: $file"
        fi
    fi
done

echo ""
if [ $remaining -eq 0 ]; then
    echo "🎉 SUCCESS! All targeted files are now free of Chinese characters!"
else
    echo "⚠️  $remaining files still contain Chinese characters and may need manual review."
fi
