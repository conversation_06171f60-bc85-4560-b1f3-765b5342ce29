export interface ListItem {
  avatar: string;
  title: string;
  datetime: string;
  type: string;
  description: string;
  status?: "primary" | "success" | "warning" | "info" | "danger";
  extra?: string;
}

export interface TabItem {
  key: string;
  name: string;
  list: ListItem[];
  emptyText: string;
}

export const noticesData: TabItem[] = [
  {
    key: "1",
    name: "Notifications",
    list: [],
    emptyText: "No notifications"
  },
  {
    key: "2",
    name: "Messages",
    list: [
      {
        avatar: "https://xiaoxian521.github.io/hyperlink/svg/smile1.svg",
        title: "<PERSON> commented on you",
        description: "Sincerity lies in the heart, trust lies in action, integrity lies in the unity of heart and action.",
        datetime: "Today",
        type: "2"
      },
      {
        avatar: "https://xiaoxian521.github.io/hyperlink/svg/smile2.svg",
        title: "<PERSON> replied to you",
        description: "There will be times when the wind and waves break, and the clouds and sails will sail across the sea.",
        datetime: "Yesterday",
        type: "2"
      },
      {
        avatar: "https://xiaoxian521.github.io/hyperlink/svg/smile5.svg",
        title: "Title",
        description:
          "Please move your mouse here to test how very long messages will be handled here. In this example, the maximum number of description lines is set to 2, and description content exceeding 2 lines will be omitted and can be viewed in full through tooltip",
        datetime: "Time",
        type: "2"
      }
    ],
    emptyText: "No messages"
  },
  {
    key: "3",
    name: "To-do",
    list: [
      {
        avatar: "",
        title: "Third-party urgent code change",
        description:
          "Submitted by Xiao Lin on 2024-05-10, code change task needs to be completed before 2024-05-11",
        datetime: "",
        extra: "About to expire",
        status: "danger",
        type: "3"
      },
      {
        avatar: "",
        title: "Version release",
        description: "Assigned to Xiao Ming to complete update and release before 2024-06-18",
        datetime: "",
        extra: "8 days elapsed",
        status: "warning",
        type: "3"
      },
      {
        avatar: "",
        title: "New feature development",
        description: "Develop multi-tenant management",
        datetime: "",
        extra: "In progress",
        type: "3"
      },
      {
        avatar: "",
        title: "Task name",
        description: "Task needs to be started before 2030-10-30 10:00",
        datetime: "",
        extra: "Not started",
        status: "info",
        type: "3"
      }
    ],
    emptyText: "No to-do items"
  }
];
