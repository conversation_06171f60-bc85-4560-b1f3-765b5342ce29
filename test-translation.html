<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation Parameter Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .format {
            font-weight: bold;
            color: #2196F3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Vue i18n Parameter Format Test</h1>
    
    <div class="test-case">
        <h3>Format 1: <span class="format">{count}</span> (Curly braces)</h3>
        <p>Template: <code>"Password must be at least {count} characters"</code></p>
        <p>Parameters: <code>{ count: 6 }</code></p>
        <div class="result">
            Expected: "Password must be at least 6 characters"
        </div>
    </div>

    <div class="test-case">
        <h3>Format 2: <span class="format">:count</span> (Colon prefix)</h3>
        <p>Template: <code>"Password must be at least :count characters"</code></p>
        <p>Parameters: <code>{ count: 6 }</code></p>
        <div class="result">
            Expected: "Password must be at least 6 characters"
        </div>
    </div>

    <div class="test-case">
        <h3>Format 3: <span class="format">{{count}}</span> (Double curly braces)</h3>
        <p>Template: <code>"Password must be at least {{count}} characters"</code></p>
        <p>Parameters: <code>{ count: 6 }</code></p>
        <div class="result">
            Expected: "Password must be at least 6 characters"
        </div>
    </div>

    <h2>Vue i18n Documentation</h2>
    <p>According to Vue i18n documentation, both formats are supported:</p>
    <ul>
        <li><strong>{key}</strong> - Named interpolation (recommended)</li>
        <li><strong>:key</strong> - Alternative named interpolation</li>
        <li><strong>{{key}}</strong> - Double braces (for compatibility)</li>
    </ul>

    <h2>Test in Browser Console</h2>
    <p>Open browser console and test with:</p>
    <pre><code>
// Test format 1: {count}
$t("Password must be at least {count} characters", { count: 6 })

// Test format 2: :count  
$t("Password must be at least :count characters", { count: 6 })

// Test format 3: {{count}}
$t("Password must be at least {{count}} characters", { count: 6 })
    </code></pre>

    <h2>Current Implementation</h2>
    <p>We are now using the <strong>:count</strong> format in our validators for consistency with the existing codebase.</p>
</body>
</html>
