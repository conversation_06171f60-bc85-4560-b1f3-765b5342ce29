<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDescriptions,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, h, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);
const formRef = ref();

const descriptionColumns = computed<PlusColumn[]>(() => [
  {
    label: $t("Model Name"),
    prop: "name"
  },
  {
    label: $t("Model Key"),
    prop: "key"
  },
  {
    label: $t("Provider"),
    prop: "provider.name"
  }
]);

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Model AI")),
    prop: "modelAI",
    renderField: (value, onChange, fieldValues) => {
      return h(PlusDescriptions, {
        data: props.values,
        columns: descriptionColumns.value,
        column: 2,
        border: true,
        size: "large",
        labelWidth: "140px",
        align: "center",
        style: { width: "100%", maxWidth: "500px", margin: "0 auto" }
      });
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Input Types")),
    prop: "service.inputTypes",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select at least one input type")
      }
    ],
    fieldProps: {
      placeholder: "",
      multiple: true
    },
    options: [
      { label: $t("Text"), value: "text" },
      { label: $t("Image"), value: "image" },
      { label: $t("Audio"), value: "audio" },
      { label: $t("Video"), value: "video" },
      { label: $t("Document"), value: "document" }
    ],
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Output Types")),
    prop: "service.outputTypes",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select at least one output type")
      }
    ],
    fieldProps: {
      placeholder: "",
      multiple: true
    },
    options: [
      { label: $t("Text"), value: "text" },
      { label: $t("Image"), value: "image" },
      { label: $t("Audio"), value: "audio" },
      { label: $t("Video"), value: "video" },
      { label: $t("Document"), value: "document" }
    ],
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Supported Sources")),
    prop: "service.supportedSources",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t(
        'e.g., [{"type":"upload"}, {"type":"oauth", "provider":"google_drive"}]'
      ),
      rows: 3
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Default Parameters")),
    prop: "service.defaultParameters",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("e.g., temperature, top_p, max_tokens"),
      rows: 3
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Allowed Parameters")),
    prop: "service.allowedParameters",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Parameters users can override"),
      rows: 3
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Context Window (Token)")),
    prop: "service.contextWindow",
    valueType: "input-number",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input context window")
      }
    ],
    fieldProps: {
      min: 0,
      precision: 0,
      placeholder: ""
    },
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Rate Limit RPM")),
    prop: "service.rateLimitRpm",
    valueType: "input-number",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input rate limit RPM")
      }
    ],
    fieldProps: {
      min: 0,
      precision: 0,
      placeholder: ""
    },
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Max Tokens")),
    prop: "service.maxTokens",
    valueType: "input-number",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input max tokens")
      }
    ],
    fieldProps: {
      min: 0,
      precision: 0,
      placeholder: ""
    },
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Timeout Seconds")),
    prop: "service.timeoutSeconds",
    valueType: "input-number",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input timeout seconds")
      }
    ],
    fieldProps: {
      min: 0,
      precision: 0,
      placeholder: ""
    },
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Billing Type")),
    prop: "service.billingType",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select billing type")
      }
    ],
    options: [
      { label: $t("Request"), value: "per_request" },
      { label: $t("Token"), value: "per_token" },
      { label: $t("Hybrid"), value: "hybrid" }
    ],
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    colProps: { span: 4 }
  },
  {
    label: computed(() => $t("Cost Per Request")),
    prop: "service.costPerRequest",
    valueType: "input-number",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input cost per request")
      }
    ],
    fieldProps: {
      min: 0,
      precision: 0,
      placeholder: ""
    },
    colProps: { span: 5 }
  },
  {
    label: computed(() => $t("Cost Per 1K Tokens")),
    prop: "service.costPer1kTokens",
    valueType: "input-number",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input cost per 1K tokens")
      }
    ],
    fieldProps: {
      min: 0,
      precision: 4,
      placeholder: ""
    },
    colProps: { span: 5 }
  },
  {
    label: computed(() => $t("Cost Per 1K Input")),
    prop: "service.costPer1kInput",
    valueType: "input-number",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input cost per 1K input")
      }
    ],
    fieldProps: {
      min: 0,
      precision: 6,
      placeholder: ""
    },
    colProps: { span: 5 }
  },
  {
    label: computed(() => $t("Cost Per 1K Output")),
    prop: "service.costPer1kOutput",
    valueType: "input-number",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input cost per 1K output")
      }
    ],
    fieldProps: {
      min: 0,
      precision: 6,
      placeholder: ""
    },
    colProps: { span: 5 }
  },
  {
    label: computed(() => $t("Note")),
    prop: "service.note",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      showWordLimit: true,
      autosize: { minRows: 3, maxRows: 6 }
    },
    colProps: { span: 24 }
  }
];

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000);
  }
};

watch(
  () => props.visible,
  value => {
    if (value) {
      console.log("--------------------->:", props.values);
    }
  }
);

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="50%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
