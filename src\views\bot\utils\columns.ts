import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag, ElAvatar } from "element-plus";
import { h } from "vue";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    prop: "logo",
    align: "center",
    width: 90,
    headerRenderer: () => "",
    cellRenderer: ({ row }) => {
      return h(ElAvatar, {
        size: 50,
        src: row.logoUrl,
        alt: row.name
      });
    }
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    width: 210,
    headerRenderer: () => $t("Bot Name")
  },
  {
    prop: "description",
    align: "left",
    minWidth: 210,
    headerRenderer: () => $t("Description"),
    cellRenderer: ({ row }) => {
      const desc = row.description || "-";
      return desc.length > 50 ? desc.substring(0, 50) + "..." : desc;
    }
  },
  {
    prop: "aiModel",
    align: "left",
    width: 140,
    headerRenderer: () => $t("Model AI"),
    cellRenderer: ({ row }) => row.aiModel?.name || "-"
  },
  {
    prop: "status",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        draft: "info",
        review: "warning",
        active: "success",
        paused: "warning",
        banned: "danger"
      };
      return h(
        ElTag,
        {
          type: statusColors[row.status] || "info",
          size: "small"
        },
        () => row.status?.toUpperCase() || "DRAFT"
      );
    }
  },
  {
    prop: "createdAt",
    align: "center",
    width: 120,
    headerRenderer: () => $t("Created At"),
    cellRenderer: ({ row }) => dayjs(row.createdAt).format("YYYY-MM-DD HH:mm")
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
