<script setup lang="ts">
import { ref, onMounted, computed, defineAsyncComponent } from "vue";
import { $t } from "@/plugins/i18n";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useECharts } from "@/utils/useECharts";
import echarts from "@/plugins/echarts";
import {
  getChatbotDashboardStats,
  getTokenTrend,
  getConversationTrend
} from "./utils/api";

defineOptions({
  name: "ChatbotDashboard"
});

// Lazy load components
const StatsCard = defineAsyncComponent(
  () => import("@/views/dashboard/components/StatsCard.vue")
);

// State
const loading = ref(false);
const timeFilter = ref("7d"); // 7d, 30d, 3m

// Chatbot Statistics
const chatbotStats = ref({
  totalBots: 0,
  activeBots: 0,
  draftBots: 0,
  mostUsedBot: ""
});

// Conversation Statistics
const conversationStats = ref({
  totalConversations: 0,
  todayConversations: 0,
  weekConversations: 0,
  avgConversationLength: 0
});

// Message Statistics
const messageStats = ref({
  totalMessages: 0,
  todayMessages: 0,
  avgResponseTime: 0,
  messageTypes: {
    text: 0,
    file: 0,
    image: 0
  }
});

// Token Statistics
const tokenStats = ref({
  totalTokens: 0,
  inputTokens: 0,
  outputTokens: 0,
  todayTokens: 0,
  estimatedCost: 0,
  avgPerConversation: 0
});

// Knowledge Base Statistics
const knowledgeStats = ref({
  totalDocuments: 0,
  totalPages: 0,
  storageUsed: 0,
  fileTypes: {
    pdf: 0,
    docx: 0,
    txt: 0,
    other: 0
  },
  mostQueriedDoc: ""
});

// Storage Statistics
const storageStats = ref({
  totalUsed: 0,
  documentsSize: 0,
  attachmentsSize: 0,
  remainingQuota: 0,
  quotaLimit: 1000
});

// Chart refs
const tokenChartRef = ref();
const conversationChartRef = ref();
const knowledgeChartRef = ref();
const storageChartRef = ref();

// Chart instances
const { setOptions: setTokenChartOptions, initChart: initTokenChartInstance } =
  useECharts(tokenChartRef);
const {
  setOptions: setConversationChartOptions,
  initChart: initConversationChartInstance
} = useECharts(conversationChartRef);
const {
  setOptions: setKnowledgeChartOptions,
  initChart: initKnowledgeChartInstance
} = useECharts(knowledgeChartRef);
const {
  setOptions: setStorageChartOptions,
  initChart: initStorageChartInstance
} = useECharts(storageChartRef);

// Computed
const storageUsagePercentage = computed(() => {
  if (storageStats.value.quotaLimit === 0) return 0;
  return Math.round(
    (storageStats.value.totalUsed / storageStats.value.quotaLimit) * 100
  );
});

const tokenCostFormatted = computed(() => {
  return `$${tokenStats.value.estimatedCost.toFixed(2)}`;
});

const storageUsedFormatted = computed(() => {
  const size = storageStats.value.totalUsed;
  if (size < 1024) return `${size.toFixed(1)} MB`;
  return `${(size / 1024).toFixed(1)} GB`;
});

// Methods
const loadDashboardData = async () => {
  loading.value = true;
  try {
    // Use mock data for demonstration (API integration can be added later)
    // TODO: Replace with actual API call when backend is ready
    // const { data: statsResponse, success } = await getChatbotDashboardStats();
    chatbotStats.value = {
      totalBots: 12,
      activeBots: 8,
      draftBots: 4,
      mostUsedBot: "Customer Support Bot"
    };

    conversationStats.value = {
      totalConversations: 245,
      todayConversations: 18,
      weekConversations: 89,
      avgConversationLength: 12.5
    };

    messageStats.value = {
      totalMessages: 3420,
      todayMessages: 156,
      avgResponseTime: 1.2,
      messageTypes: {
        text: 2890,
        file: 380,
        image: 150
      }
    };

    tokenStats.value = {
      totalTokens: 125680,
      inputTokens: 45230,
      outputTokens: 80450,
      todayTokens: 1250,
      estimatedCost: 15.75,
      avgPerConversation: 340
    };

    knowledgeStats.value = {
      totalDocuments: 45,
      totalPages: 1250,
      storageUsed: 125.5,
      fileTypes: {
        pdf: 25,
        docx: 12,
        txt: 8,
        other: 5
      },
      mostQueriedDoc: "Product Manual.pdf"
    };

    storageStats.value = {
      totalUsed: 245.8,
      documentsSize: 125.5,
      attachmentsSize: 120.3,
      remainingQuota: 754.2,
      quotaLimit: 1000
    };

    // Charts will be initialized in onMounted
  } finally {
    loading.value = false;
  }
};

const initCharts = async () => {
  console.log("Starting chart initialization...");

  // Test direct ECharts initialization
  if (tokenChartRef.value && echarts) {
    try {
      const testChart = echarts.init(tokenChartRef.value);
      console.log("Direct ECharts init successful:", testChart);

      testChart.setOption({
        title: { text: "Test Chart" },
        xAxis: { type: "category", data: ["A", "B", "C"] },
        yAxis: { type: "value" },
        series: [{ data: [120, 200, 150], type: "line" }]
      });

      // Dispose test chart
      setTimeout(() => {
        testChart.dispose();
        // Now try with useECharts hook
        initChartsWithHook();
      }, 2000);
    } catch (error) {
      console.error("Direct ECharts init failed:", error);
    }
  } else {
    console.error("tokenChartRef or echarts not available");
  }
};

const initChartsWithHook = async () => {
  console.log("Initializing charts with hook...");

  // Initialize chart instances first
  await Promise.all([
    initTokenChartInstance(),
    initConversationChartInstance(),
    initKnowledgeChartInstance(),
    initStorageChartInstance()
  ]);

  // Then set chart options
  initTokenChart();
  initConversationChart();
  initKnowledgeChart();
  initStorageChart();
};

const initTokenChart = () => {
  console.log("Initializing token chart...", tokenChartRef.value);
  const option = {
    title: {
      text: "Token Usage Trend",
      textStyle: {
        fontSize: 14,
        fontWeight: "normal"
      }
    },
    tooltip: {
      trigger: "axis"
    },
    legend: {
      data: ["Input Tokens", "Output Tokens"]
    },
    xAxis: {
      type: "category",
      data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        name: "Input Tokens",
        type: "line",
        data: [1200, 1500, 1800, 1400, 1600, 1900, 1250],
        smooth: true,
        itemStyle: { color: "#409EFF" }
      },
      {
        name: "Output Tokens",
        type: "line",
        data: [2100, 2400, 2800, 2200, 2600, 3100, 2050],
        smooth: true,
        itemStyle: { color: "#67C23A" }
      }
    ]
  };
  console.log("Setting token chart options...", option);
  setTokenChartOptions(option);
};

const initConversationChart = () => {
  const option = {
    title: {
      text: "Conversations Trend",
      textStyle: {
        fontSize: 14,
        fontWeight: "normal"
      }
    },
    tooltip: {
      trigger: "axis"
    },
    xAxis: {
      type: "category",
      data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        name: "Conversations",
        type: "bar",
        data: [12, 18, 15, 22, 19, 25, 18],
        itemStyle: {
          color: "#E6A23C",
          borderRadius: [4, 4, 0, 0]
        }
      }
    ]
  };
  setConversationChartOptions(option);
};

const initKnowledgeChart = () => {
  const option = {
    title: {
      text: "Knowledge Base Files",
      textStyle: {
        fontSize: 14,
        fontWeight: "normal"
      }
    },
    tooltip: {
      trigger: "item"
    },
    series: [
      {
        name: "File Types",
        type: "pie",
        radius: "70%",
        data: [
          { value: knowledgeStats.value.fileTypes.pdf, name: "PDF" },
          { value: knowledgeStats.value.fileTypes.docx, name: "DOCX" },
          { value: knowledgeStats.value.fileTypes.txt, name: "TXT" },
          { value: knowledgeStats.value.fileTypes.other, name: "Other" }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        }
      }
    ]
  };
  setKnowledgeChartOptions(option);
};

const initStorageChart = () => {
  const option = {
    title: {
      text: "Storage Usage",
      textStyle: {
        fontSize: 14,
        fontWeight: "normal"
      }
    },
    tooltip: {
      trigger: "item"
    },
    series: [
      {
        name: "Storage",
        type: "pie",
        radius: ["40%", "70%"],
        data: [
          { value: storageStats.value.documentsSize, name: "Documents" },
          { value: storageStats.value.attachmentsSize, name: "Attachments" },
          { value: storageStats.value.remainingQuota, name: "Available" }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        }
      }
    ]
  };
  setStorageChartOptions(option);
};

const handleTimeFilterChange = (filter: string) => {
  timeFilter.value = filter;
  loadDashboardData();
};

const exportData = () => {
  // TODO: Implement data export functionality
  console.log("Exporting dashboard data...");
};

onMounted(async () => {
  console.log("Welcome component mounted");
  console.log("ECharts available:", !!echarts);
  await loadDashboardData();

  // Initialize charts after component is fully mounted
  setTimeout(async () => {
    console.log("Initializing charts after mount...");
    console.log("Chart refs:", {
      token: !!tokenChartRef.value,
      conversation: !!conversationChartRef.value,
      knowledge: !!knowledgeChartRef.value,
      storage: !!storageChartRef.value
    });
    await initCharts();
  }, 1000);
});
</script>

<template>
  <div class="dashboard-container">
    <!-- Hero Header Section -->
    <div class="hero-header">
      <div class="hero-content">
        <div class="hero-text">
          <div class="hero-badge">
            <iconify-icon-offline
              :icon="useRenderIcon('ri/dashboard-3-line')"
              class="badge-icon"
            />
            <span>Analytics Dashboard</span>
          </div>
          <h1 class="hero-title">
            Chatbot Performance
            <span class="title-gradient">Insights</span>
          </h1>
          <p class="hero-subtitle">
            Comprehensive analytics and performance metrics for your AI chatbots
          </p>
        </div>

        <div class="hero-actions">
          <div class="time-filter-wrapper">
            <label class="filter-label">Time Period</label>
            <el-radio-group
              v-model="timeFilter"
              class="modern-radio-group"
              @change="handleTimeFilterChange"
            >
              <el-radio-button value="7d">7 Days</el-radio-button>
              <el-radio-button value="30d">30 Days</el-radio-button>
              <el-radio-button value="3m">3 Months</el-radio-button>
            </el-radio-group>
          </div>

          <el-button
            type="primary"
            class="export-button"
            :icon="useRenderIcon('ep:download')"
            @click="exportData"
          >
            Export Report
          </el-button>
        </div>
      </div>
    </div>

    <!-- Key Metrics Overview -->
    <div class="metrics-section">
      <div class="section-header">
        <h2 class="section-title">
          <iconify-icon-offline
            :icon="useRenderIcon('ri/bar-chart-box-line')"
            class="section-icon"
          />
          Performance Overview
        </h2>
        <p class="section-subtitle">
          Real-time insights into your chatbot ecosystem
        </p>
      </div>

      <div class="metrics-grid">
        <!-- Primary Metric - Featured -->
        <div class="metric-card featured-metric">
          <div class="metric-background">
            <div class="background-pattern" />
          </div>
          <div class="metric-header">
            <div class="metric-icon-wrapper primary">
              <iconify-icon-offline
                :icon="useRenderIcon('ri/robot-2-line')"
                class="metric-icon"
              />
            </div>
            <div class="metric-trend positive">
              <iconify-icon-offline :icon="useRenderIcon('ri/arrow-up-line')" />
              <span>+12%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-value">{{ chatbotStats.totalBots }}</h3>
            <p class="metric-label">Active Chatbots</p>
            <div class="metric-details">
              <div class="detail-item">
                <span class="detail-value">{{ chatbotStats.activeBots }}</span>
                <span class="detail-label">Active</span>
              </div>
              <div class="detail-separator" />
              <div class="detail-item">
                <span class="detail-value">{{ chatbotStats.draftBots }}</span>
                <span class="detail-label">Draft</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Secondary Metrics -->
        <div class="metric-card standard-metric">
          <div class="metric-header">
            <div class="metric-icon-wrapper success">
              <iconify-icon-offline
                :icon="useRenderIcon('ri/chat-3-line')"
                class="metric-icon"
              />
            </div>
            <div class="metric-trend positive">
              <iconify-icon-offline :icon="useRenderIcon('ri/arrow-up-line')" />
              <span>+8%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-value">
              {{ conversationStats.totalConversations.toLocaleString() }}
            </h3>
            <p class="metric-label">Conversations</p>
            <div class="metric-subtitle">
              {{ conversationStats.todayConversations }} today
            </div>
          </div>
        </div>

        <div class="metric-card standard-metric">
          <div class="metric-header">
            <div class="metric-icon-wrapper warning">
              <iconify-icon-offline
                :icon="useRenderIcon('ri/cpu-line')"
                class="metric-icon"
              />
            </div>
            <div class="metric-trend neutral">
              <iconify-icon-offline :icon="useRenderIcon('ri/subtract-line')" />
              <span>0%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-value">
              {{ tokenStats.totalTokens.toLocaleString() }}
            </h3>
            <p class="metric-label">Tokens Used</p>
            <div class="metric-subtitle">
              ${{ tokenStats.estimatedCost }} cost
            </div>
          </div>
        </div>

        <div class="metric-card standard-metric">
          <div class="metric-header">
            <div class="metric-icon-wrapper info">
              <iconify-icon-offline
                :icon="useRenderIcon('ri/hard-drive-line')"
                class="metric-icon"
              />
            </div>
            <div class="metric-trend positive">
              <iconify-icon-offline :icon="useRenderIcon('ri/arrow-up-line')" />
              <span>+5%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-value">{{ storageUsedFormatted }}</h3>
            <p class="metric-label">Storage Used</p>
            <div class="metric-subtitle">
              {{ knowledgeStats.totalDocuments }} documents
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Analytics Charts -->
    <div class="charts-section">
      <div class="section-header">
        <h2 class="section-title">
          <iconify-icon-offline
            :icon="useRenderIcon('ri/line-chart-line')"
            class="section-icon"
          />
          Trend Analysis
        </h2>
        <p class="section-subtitle">
          Visualize your chatbot performance over time
        </p>
      </div>

      <div class="charts-grid">
        <!-- Token Usage Chart -->
        <div class="chart-card primary-chart">
          <div class="chart-header">
            <div class="chart-title-wrapper">
              <h3 class="chart-title">Token Usage Trend</h3>
              <p class="chart-subtitle">Daily token consumption patterns</p>
            </div>
            <div class="chart-actions">
              <el-tag type="info" size="small">Last 30 days</el-tag>
            </div>
          </div>
          <div class="chart-content">
            <div ref="tokenChartRef" class="chart-container" />
          </div>
        </div>

        <!-- Conversation Trend Chart -->
        <div class="chart-card secondary-chart">
          <div class="chart-header">
            <div class="chart-title-wrapper">
              <h3 class="chart-title">Conversation Volume</h3>
              <p class="chart-subtitle">Daily conversation activity</p>
            </div>
            <div class="chart-actions">
              <el-tag type="success" size="small">+8% growth</el-tag>
            </div>
          </div>
          <div class="chart-content">
            <div ref="conversationChartRef" class="chart-container" />
          </div>
        </div>
      </div>
    </div>

    <!-- Data Insights Section -->
    <div class="insights-section">
      <div class="section-header">
        <h2 class="section-title">
          <iconify-icon-offline
            :icon="useRenderIcon('ri/database-2-line')"
            class="section-icon"
          />
          Data & Storage Insights
        </h2>
        <p class="section-subtitle">
          Knowledge base composition and storage utilization
        </p>
      </div>

      <div class="insights-grid">
        <!-- Knowledge Base Chart -->
        <div class="insight-card knowledge-card">
          <div class="insight-header">
            <div class="insight-title-wrapper">
              <h3 class="insight-title">Knowledge Base</h3>
              <p class="insight-subtitle">Document type distribution</p>
            </div>
            <div class="insight-stats">
              <div class="stat-item">
                <span class="stat-value">{{
                  knowledgeStats.totalDocuments
                }}</span>
                <span class="stat-label">Documents</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ knowledgeStats.totalPages }}</span>
                <span class="stat-label">Pages</span>
              </div>
            </div>
          </div>
          <div class="insight-content">
            <div ref="knowledgeChartRef" class="chart-container" />
            <div class="insight-footer">
              <div class="most-queried">
                <iconify-icon-offline
                  :icon="useRenderIcon('ri/star-line')"
                  class="star-icon"
                />
                <span class="queried-label">Most Queried:</span>
                <span class="queried-doc">{{
                  knowledgeStats.mostQueriedDoc
                }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Storage Usage Chart -->
        <div class="insight-card storage-card">
          <div class="insight-header">
            <div class="insight-title-wrapper">
              <h3 class="insight-title">Storage Usage</h3>
              <p class="insight-subtitle">Space utilization breakdown</p>
            </div>
            <div class="storage-indicator">
              <div
                class="usage-badge"
                :class="storageUsagePercentage > 80 ? 'danger' : 'success'"
              >
                {{ storageUsagePercentage }}% Used
              </div>
            </div>
          </div>
          <div class="insight-content">
            <div ref="storageChartRef" class="chart-container" />
            <div class="insight-footer">
              <div class="storage-progress">
                <div class="progress-info">
                  <span class="used-space">{{ storageUsedFormatted }}</span>
                  <span class="total-space"
                    >/ {{ (storageStats.quotaLimit / 1024).toFixed(1) }}GB</span
                  >
                </div>
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{ width: storageUsagePercentage + '%' }"
                    :class="storageUsagePercentage > 80 ? 'danger' : 'success'"
                  />
                </div>
                <div class="remaining-space">
                  {{ (storageStats.remainingQuota / 1024).toFixed(1) }}GB
                  remaining
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance & Activity Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Top Performing Bots -->
      <el-card class="performance-card" shadow="hover">
        <template #header>
          <div class="flex items-center justify-between">
            <span class="font-semibold">🤖 Top Chatbot Performance</span>
            <el-tag size="small" type="success">Hiệu suất cao</el-tag>
          </div>
        </template>
        <div class="space-y-4">
          <div
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <div
                class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"
              >
                <IconifyIconOffline
                  :icon="useRenderIcon('ri/robot-line')"
                  class="text-blue-600"
                />
              </div>
              <div>
                <p class="font-medium text-gray-900">Customer Support Bot</p>
                <p class="text-sm text-gray-600">245 cuộc hội thoại</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-green-600">+15.2%</p>
              <p class="text-xs text-gray-500">so với tuần trước</p>
            </div>
          </div>

          <div
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <div
                class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center"
              >
                <IconifyIconOffline
                  :icon="useRenderIcon('ri/service-line')"
                  class="text-green-600"
                />
              </div>
              <div>
                <p class="font-medium text-gray-900">Sales Assistant</p>
                <p class="text-sm text-gray-600">189 cuộc hội thoại</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-green-600">+8.7%</p>
              <p class="text-xs text-gray-500">so với tuần trước</p>
            </div>
          </div>

          <div
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <div
                class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center"
              >
                <IconifyIconOffline
                  :icon="useRenderIcon('ri/question-answer-line')"
                  class="text-purple-600"
                />
              </div>
              <div>
                <p class="font-medium text-gray-900">FAQ Bot</p>
                <p class="text-sm text-gray-600">156 cuộc hội thoại</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-red-600">-2.1%</p>
              <p class="text-xs text-gray-500">so với tuần trước</p>
            </div>
          </div>
        </div>
      </el-card>

      <!-- Recent Activities -->
      <el-card class="activity-card" shadow="hover">
        <template #header>
          <div class="flex items-center justify-between">
            <span class="font-semibold">📝 Hoạt động Gần đây</span>
            <el-button size="small" text type="primary">Xem tất cả</el-button>
          </div>
        </template>
        <div class="space-y-4">
          <div class="flex items-start space-x-3">
            <div
              class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ri/chat-new-line')"
                class="text-blue-600 text-sm"
              />
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">
                Cuộc hội thoại mới
              </p>
              <p class="text-xs text-gray-600">
                Customer Support Bot - 5 phút trước
              </p>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <div
              class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ri/file-add-line')"
                class="text-green-600 text-sm"
              />
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">
                Tài liệu mới được thêm
              </p>
              <p class="text-xs text-gray-600">
                Product_Guide_v2.pdf - 1 giờ trước
              </p>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <div
              class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ri/robot-2-line')"
                class="text-yellow-600 text-sm"
              />
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">
                Chatbot được cập nhật
              </p>
              <p class="text-xs text-gray-600">Sales Assistant - 3 giờ trước</p>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <div
              class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ri/coin-line')"
                class="text-purple-600 text-sm"
              />
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">Token usage cao</p>
              <p class="text-xs text-gray-600">1,250 tokens trong 1 giờ qua</p>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <div
              class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ri/alert-line')"
                class="text-red-600 text-sm"
              />
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">
                Cảnh báo dung lượng
              </p>
              <p class="text-xs text-gray-600">
                Đã sử dụng 85% dung lượng - 6 giờ trước
              </p>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- Quick Actions -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-8">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">
        🚀 Hành động Nhanh
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <el-button
          type="primary"
          class="h-12"
          @click="$router.push('/bots/agent')"
        >
          <IconifyIconOffline
            :icon="useRenderIcon('ri/add-line')"
            class="mr-2"
          />
          Tạo Chatbot Mới
        </el-button>

        <el-button type="success" class="h-12" @click="$router.push('/chat')">
          <IconifyIconOffline
            :icon="useRenderIcon('ri/chat-3-line')"
            class="mr-2"
          />
          Bắt đầu Chat
        </el-button>

        <el-button type="warning" class="h-12" @click="$router.push('/bots')">
          <IconifyIconOffline
            :icon="useRenderIcon('ri/settings-3-line')"
            class="mr-2"
          />
          Quản lý Bot
        </el-button>

        <el-button type="info" class="h-12" @click="exportData">
          <IconifyIconOffline
            :icon="useRenderIcon('ri/download-line')"
            class="mr-2"
          />
          Xuất Báo cáo
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dashboard-container {
  min-height: 100vh;
  background: #f8fafc;

  // Hero Header Section
  .hero-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 3rem 2rem 4rem;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(
          circle at 20% 50%,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 50%
        ),
        radial-gradient(
          circle at 80% 20%,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 50%
        );
      opacity: 0.6;
    }

    .hero-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 2rem;
      position: relative;
      z-index: 1;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
        text-align: center;
      }
    }

    .hero-text {
      flex: 1;

      .hero-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50px;
        padding: 0.5rem 1rem;
        color: white;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 1.5rem;

        .badge-icon {
          font-size: 1rem;
        }
      }

      .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        color: white;
        line-height: 1.1;
        margin-bottom: 1rem;

        @media (max-width: 768px) {
          font-size: 2.5rem;
        }

        .title-gradient {
          background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .hero-subtitle {
        font-size: 1.25rem;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.6;
        max-width: 500px;
      }
    }

    .hero-actions {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      align-items: flex-end;

      @media (max-width: 768px) {
        align-items: center;
        width: 100%;
      }

      .time-filter-wrapper {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .filter-label {
          color: rgba(255, 255, 255, 0.8);
          font-size: 0.875rem;
          font-weight: 500;
        }
      }

      .export-button {
        background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
        border: none;
        color: #2d3436;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(255, 234, 167, 0.4);
        }
      }
    }
  }

  // Main Content Sections
  .metrics-section,
  .charts-section,
  .insights-section {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;

    .section-header {
      margin-bottom: 2rem;
      text-align: center;

      .section-title {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        font-size: 2rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;

        .section-icon {
          font-size: 2rem;
          color: #667eea;
        }
      }

      .section-subtitle {
        font-size: 1.125rem;
        color: #6b7280;
        max-width: 600px;
        margin: 0 auto;
      }
    }
  }

  // Metrics Grid
  .metrics-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1.5rem;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr 1fr;
    }

    @media (max-width: 640px) {
      grid-template-columns: 1fr;
    }
  }

  .metric-card {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    }

    &.featured-metric {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      .metric-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0.1;

        .background-pattern {
          width: 100%;
          height: 100%;
          background: radial-gradient(
            circle at 70% 30%,
            rgba(255, 255, 255, 0.3) 0%,
            transparent 50%
          );
        }
      }

      .metric-value {
        color: white;
      }

      .metric-label {
        color: rgba(255, 255, 255, 0.9);
      }

      .detail-value {
        color: white;
      }

      .detail-label {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .metric-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;
      position: relative;
      z-index: 1;
    }

    .metric-icon-wrapper {
      width: 3rem;
      height: 3rem;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      .metric-icon {
        font-size: 1.5rem;
      }

      &.primary {
        background: rgba(255, 255, 255, 0.2);
        .metric-icon {
          color: white;
        }
      }

      &.success {
        background: #dcfce7;
        .metric-icon {
          color: #16a34a;
        }
      }

      &.warning {
        background: #fef3c7;
        .metric-icon {
          color: #d97706;
        }
      }

      &.info {
        background: #dbeafe;
        .metric-icon {
          color: #2563eb;
        }
      }
    }

    .metric-trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border-radius: 8px;
      font-size: 0.75rem;
      font-weight: 600;

      &.positive {
        background: #dcfce7;
        color: #16a34a;
      }

      &.neutral {
        background: #f3f4f6;
        color: #6b7280;
      }

      .featured-metric & {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }
    }

    .metric-content {
      position: relative;
      z-index: 1;
    }

    .metric-value {
      font-size: 2.5rem;
      font-weight: 800;
      color: #1f2937;
      line-height: 1;
      margin-bottom: 0.5rem;
    }

    .metric-label {
      font-size: 1rem;
      font-weight: 600;
      color: #6b7280;
      margin-bottom: 1rem;
    }

    .metric-subtitle {
      font-size: 0.875rem;
      color: #9ca3af;
      margin-top: 0.5rem;
    }

    .metric-details {
      display: flex;
      align-items: center;
      gap: 1rem;

      .detail-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .detail-value {
          font-size: 1.25rem;
          font-weight: 700;
          color: #1f2937;
        }

        .detail-label {
          font-size: 0.75rem;
          color: #9ca3af;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
      }

      .detail-separator {
        width: 1px;
        height: 2rem;
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  // Charts Section
  .charts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .chart-card {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1.5rem;

      .chart-title-wrapper {
        .chart-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 0.25rem;
        }

        .chart-subtitle {
          font-size: 0.875rem;
          color: #6b7280;
        }
      }

      .chart-actions {
        .el-tag {
          border-radius: 8px;
          font-weight: 500;
        }
      }
    }

    .chart-content {
      .chart-container {
        height: 300px;
        width: 100%;
      }
    }
  }

  // Insights Section
  .insights-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .insight-card {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    }

    .insight-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1.5rem;

      .insight-title-wrapper {
        .insight-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 0.25rem;
        }

        .insight-subtitle {
          font-size: 0.875rem;
          color: #6b7280;
        }
      }

      .insight-stats {
        display: flex;
        gap: 1rem;

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .stat-value {
            font-size: 1.125rem;
            font-weight: 700;
            color: #1f2937;
          }

          .stat-label {
            font-size: 0.75rem;
            color: #9ca3af;
            text-transform: uppercase;
            letter-spacing: 0.05em;
          }
        }
      }

      .storage-indicator {
        .usage-badge {
          padding: 0.25rem 0.75rem;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 600;

          &.success {
            background: #dcfce7;
            color: #16a34a;
          }

          &.danger {
            background: #fee2e2;
            color: #dc2626;
          }
        }
      }
    }

    .insight-content {
      .chart-container {
        height: 250px;
        width: 100%;
        margin-bottom: 1rem;
      }
    }

    .insight-footer {
      .most-queried {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem;
        background: #f8fafc;
        border-radius: 12px;

        .star-icon {
          color: #fbbf24;
          font-size: 1rem;
        }

        .queried-label {
          font-size: 0.875rem;
          color: #6b7280;
          font-weight: 500;
        }

        .queried-doc {
          font-size: 0.875rem;
          color: #1f2937;
          font-weight: 600;
        }
      }

      .storage-progress {
        .progress-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.5rem;

          .used-space {
            font-size: 1rem;
            font-weight: 600;
            color: #1f2937;
          }

          .total-space {
            font-size: 0.875rem;
            color: #6b7280;
          }
        }

        .progress-bar {
          width: 100%;
          height: 8px;
          background: #f1f5f9;
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 0.5rem;

          .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: all 0.3s ease;

            &.success {
              background: linear-gradient(90deg, #16a34a 0%, #22c55e 100%);
            }

            &.danger {
              background: linear-gradient(90deg, #dc2626 0%, #ef4444 100%);
            }
          }
        }

        .remaining-space {
          font-size: 0.75rem;
          color: #9ca3af;
          text-align: center;
        }
      }
    }
  }

  .performance-card,
  .activity-card {
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    :deep(.el-card__header) {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      border-radius: 12px 12px 0 0;
      padding: 16px 20px;
      border-bottom: none;

      .font-semibold {
        color: white;
        font-weight: 600;
      }

      .el-tag {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
      }

      .el-button {
        color: white;
        border-color: rgba(255, 255, 255, 0.3);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  // Stats cards styling
  :deep(.stats-card) {
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .el-card__body {
      padding: 20px;
    }
  }

  // Radio button group styling
  :deep(.el-radio-group) {
    .el-radio-button__inner {
      border-radius: 8px;
      margin: 0 2px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.8);
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
        color: white;
      }
    }

    .el-radio-button__original-radio:checked + .el-radio-button__inner {
      background: white;
      border-color: white;
      color: #667eea;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      font-weight: 600;
    }
  }

  // Modern radio group for hero section
  .modern-radio-group {
    :deep(.el-radio-group) {
      .el-radio-button__inner {
        border-radius: 8px;
        margin: 0 2px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.4);
          color: white;
        }
      }

      .el-radio-button__original-radio:checked + .el-radio-button__inner {
        background: white;
        border-color: white;
        color: #667eea;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-weight: 600;
      }
    }
  }

  // Progress bar styling
  :deep(.el-progress-bar__outer) {
    border-radius: 10px;
    background: #f3f4f6;
  }

  :deep(.el-progress-bar__inner) {
    border-radius: 10px;
    transition: all 0.3s ease;
  }

  // Tag styling
  :deep(.el-tag) {
    border-radius: 6px;
    font-weight: 500;
  }

  // Button styling
  :deep(.el-button) {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }

  // Responsive design
  @media (max-width: 1024px) {
    .hero-header {
      padding: 2rem 1.5rem 3rem;

      .hero-title {
        font-size: 3rem;
      }
    }

    .metrics-section,
    .charts-section,
    .insights-section {
      padding: 1.5rem;
    }
  }

  @media (max-width: 768px) {
    .hero-header {
      padding: 2rem 1rem 2.5rem;

      .hero-title {
        font-size: 2.5rem;
      }

      .hero-subtitle {
        font-size: 1.125rem;
      }
    }

    .metrics-section,
    .charts-section,
    .insights-section {
      padding: 1rem;

      .section-title {
        font-size: 1.75rem;
      }

      .section-subtitle {
        font-size: 1rem;
      }
    }

    .chart-container {
      height: 250px;
    }

    .metric-card {
      padding: 1rem;

      .metric-value {
        font-size: 2rem;
      }
    }

    .chart-card,
    .insight-card {
      padding: 1rem;
    }
  }

  @media (max-width: 640px) {
    .hero-header {
      .hero-title {
        font-size: 2rem;
      }

      .hero-actions {
        width: 100%;
        align-items: center;

        .time-filter-wrapper {
          width: 100%;
          align-items: center;
        }
      }
    }

    .metric-details {
      flex-direction: column;
      gap: 0.5rem;

      .detail-separator {
        display: none;
      }
    }
  }

  // Animation for loading states
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  // Smooth scrolling
  html {
    scroll-behavior: smooth;
  }

  // Custom scrollbar
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;

    &:hover {
      background: #94a3b8;
    }
  }

  // Gradient backgrounds
  .bg-gradient-to-r {
    background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
    border: 1px solid #c7d2fe;
  }

  // Custom scrollbar for activity list
  .space-y-4 {
    max-height: 400px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 2px;

      &:hover {
        background: #94a3b8;
      }
    }
  }
}
</style>
