<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Refresh Token Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Auto Refresh Token Test</h1>
    <p>Test để kiểm tra xem token có tự động refresh khi hết hạn không</p>
    
    <div class="status" id="status">Ready to test</div>
    
    <button onclick="startTest()">Start Test</button>
    <button onclick="clearLogs()">Clear Logs</button>
    
    <div id="logs"></div>

    <script>
        let testInterval;
        let tokenData = null;
        
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(logDiv);
            logs.scrollTop = logs.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        // Simulate login with short-lived token
        function simulateLogin() {
            log('Simulating login...', 'info');
            
            const loginResponse = {
                success: true,
                data: {
                    user: {
                        username: "admin",
                        firstName: "Test",
                        lastName: "User",
                        fullName: "Test User",
                        permissions: ["*:*:*"],
                        roles: ["admin"]
                    },
                    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_token",
                    tokenType: "Bearer",
                    expiresIn: 10 // 10 seconds for quick testing
                }
            };
            
            // Simulate setToken logic
            const now = Date.now();
            tokenData = {
                accessToken: loginResponse.data.token,
                refreshToken: loginResponse.data.token,
                expires: now + (loginResponse.data.expiresIn * 1000)
            };
            
            log(`Token created: expires in ${loginResponse.data.expiresIn} seconds`, 'success');
            log(`Expires at: ${new Date(tokenData.expires).toLocaleTimeString()}`, 'info');
            
            return tokenData;
        }
        
        // Simulate refresh token
        function simulateRefreshToken() {
            log('Token expired! Attempting refresh...', 'warning');
            
            // Simulate API call delay
            return new Promise((resolve) => {
                setTimeout(() => {
                    const refreshResponse = {
                        success: true,
                        data: {
                            accessToken: "eyJhbGciOiJIUzUxMiJ9.new_token",
                            refreshToken: "eyJhbGciOiJIUzUxMiJ9.new_refresh",
                            expires: "2030/10/30 23:59:59"
                        }
                    };
                    
                    // Update token data
                    tokenData = {
                        accessToken: refreshResponse.data.accessToken,
                        refreshToken: refreshResponse.data.refreshToken,
                        expires: new Date(refreshResponse.data.expires).getTime()
                    };
                    
                    log('Token refreshed successfully!', 'success');
                    log(`New token expires: ${new Date(tokenData.expires).toLocaleString()}`, 'info');
                    
                    resolve(tokenData);
                }, 1000); // Simulate 1 second API delay
            });
        }
        
        // Check token expiry
        function checkTokenExpiry() {
            if (!tokenData) return false;
            
            const now = Date.now();
            const timeRemaining = tokenData.expires - now;
            const expired = timeRemaining <= 0;
            
            if (timeRemaining > 0) {
                log(`Token valid - ${Math.ceil(timeRemaining / 1000)} seconds remaining`);
                updateStatus(`Token valid - ${Math.ceil(timeRemaining / 1000)}s remaining`, 'success');
            } else {
                log(`Token expired ${Math.abs(Math.floor(timeRemaining / 1000))} seconds ago`, 'error');
                updateStatus('Token expired!', 'error');
            }
            
            return expired;
        }
        
        // Main test function
        async function startTest() {
            clearLogs();
            log('Starting auto refresh token test...', 'info');
            
            // Step 1: Login
            simulateLogin();
            
            // Step 2: Start monitoring
            let refreshAttempted = false;
            
            testInterval = setInterval(async () => {
                const expired = checkTokenExpiry();
                
                if (expired && !refreshAttempted) {
                    refreshAttempted = true;
                    
                    try {
                        await simulateRefreshToken();
                        updateStatus('Token refreshed successfully', 'success');
                        
                        // Continue monitoring with new token
                        refreshAttempted = false;
                        
                    } catch (error) {
                        log('Refresh token failed!', 'error');
                        updateStatus('Refresh failed - would logout user', 'error');
                        clearInterval(testInterval);
                    }
                }
            }, 1000); // Check every second
            
            // Auto stop after 30 seconds
            setTimeout(() => {
                if (testInterval) {
                    clearInterval(testInterval);
                    log('Test completed after 30 seconds', 'info');
                    updateStatus('Test completed', 'info');
                }
            }, 30000);
        }
        
        // Stop test when page unloads
        window.addEventListener('beforeunload', () => {
            if (testInterval) {
                clearInterval(testInterval);
            }
        });
    </script>
</body>
</html>
