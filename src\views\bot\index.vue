<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useBotHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";
import { PlusDialog } from "plus-pro-components";

const BotFilterForm = defineAsyncComponent(
  () => import("./components/BotFilterForm.vue")
);

const BotForm = defineAsyncComponent(
  () => import("./components/BotDialogForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const useBot = useBotHook();

const { drawerVisible, drawerValues, filterVisible, fnGetBots, handleSubmit } =
  useBot;

onMounted(() => {
  fnGetBots();
});
</script>

<template>
  <div class="main">
    <div ref="contentRef">
      <PureTableBar
        :title="$t('Bot Management')"
        :columns="columns"
        @refresh="fnGetBots"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('language.create')"
              @click="
                () => {
                  drawerVisible = true;
                  console.log('*-------->::', drawerVisible);
                }
              "
            >
              <IconifyIconOnline
                :icon="
                  hasAuth('language.create')
                    ? 'flat-color-icons:plus'
                    : 'icons8:plus'
                "
                width="18px"
              />
            </el-button>
          </el-tooltip>
          <el-tooltip :content="$t('Delete')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="
                useBot.multipleSelection.value.length === 0 ||
                (useBot.multipleSelection.value.length > 0 &&
                  !hasAuth('bot.delete'))
              "
              @click="useBot.handleBulkDelete"
            >
              <IconifyIconOnline
                icon="tabler:trash-x-filled"
                width="18px"
                class="text-red-600"
              />
            </el-button>
          </el-tooltip>
        </template>

        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            row-key="id"
            adaptive
            border
            :adaptiveConfig="{ offsetBottom: 108 }"
            align-whole="center"
            table-layout="auto"
            :loading="useBot.loading.value"
            :size="size"
            :data="useBot.records.value"
            :columns="dynamicColumns"
            :pagination="useBot.pagination"
            :paginationSmall="size === 'small'"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="useBot.fnHandleSelectionChange"
            @page-size-change="useBot.fnHandleSizeChange"
            @page-current-change="useBot.fnHandlePageChange"
            @sort-change="useBot.fnHandleSortChange"
          >
            <template #operation="{ row }">
              <el-dropdown split-button trigger="click" size="small">
                {{ $t("Action") }}
                <template #dropdown>
                  <el-dropdown-menu class="min-w-[130px]">
                    <el-dropdown-item disabled>
                      {{ $t("Action") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      :disabled="!hasAuth('language.update')"
                      @click="
                        () => {
                          useBot.drawerValues.value = clone(row, true);
                          useBot.drawerVisible.value = true;
                        }
                      "
                    >
                      <IconifyIconOnline
                        icon="material-symbols:edit"
                        class="text-blue-600"
                      />
                      <span class="ml-2">
                        {{ $t("Edit") }}
                      </span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      :disabled="!hasAuth('language.force-delete')"
                      @click="useBot.handleDelete(row.id)"
                    >
                      <IconifyIconOnline
                        icon="tabler:trash"
                        class="text-red-800"
                      />
                      <span class="ml-2">
                        {{ $t("Destroy") }}
                      </span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>
    <BotFilterForm ref="filterFormRef" :use-bot="useBot" />
    <PlusDialog
      ref="dialogFormRef"
      v-model="drawerVisible"
      width="90%"
      top="10px"
      title=""
      :has-footer="false"
      :closeOnClickModal="false"
      :closeOnPressEscape="true"
      :draggable="false"
    >
      <BotForm ref="botFormRef" :use-bot="useBot" />
    </PlusDialog>
  </div>
</template>
