import { Plugin as importToCDN } from "vite-plugin-cdn-import";

/**
 * @description Use `cdn` mode when building, only for external network use (not used by default, if you need to use cdn mode, please set VITE_CDN to true in .env.production file)
 * Platform uses domestic cdn: https://www.bootcdn.cn, of course you can also choose https://unpkg.com or https://www.jsdelivr.com
 * Note: The above mentioned external network use only is not completely certain, if your company's intranet has deployed relevant js, css files, you can also change the configuration below accordingly, to create an intranet version cdn
 */
export const cdn = importToCDN({
  //(prodUrl explanation: name: corresponds to the name of modules below, version: automatically reads the version number of the corresponding package in dependencies in local package.json, path: corresponds to the path of modules below, of course you can also write the full path, will replace prodUrl)
  prodUrl: "https://cdn.bootcdn.net/ajax/libs/{name}/{version}/{path}",
  modules: [
    {
      name: "vue",
      var: "Vue",
      path: "vue.global.prod.min.js"
    },
    {
      name: "vue-router",
      var: "VueRouter",
      path: "vue-router.global.min.js"
    },
    // vue-demi is not directly installed in the project, but pinia uses it, so vue-demi needs to be imported before importing pinia (https://github.com/vuejs/pinia/blob/v2/packages/pinia/package.json#L77)
    {
      name: "vue-demi",
      var: "VueDemi",
      path: "index.iife.min.js"
    },
    {
      name: "pinia",
      var: "Pinia",
      path: "pinia.iife.min.js"
    },
    {
      name: "element-plus",
      var: "ElementPlus",
      path: "index.full.min.js",
      css: "index.min.css"
    },
    {
      name: "axios",
      var: "axios",
      path: "axios.min.js"
    },
    {
      name: "dayjs",
      var: "dayjs",
      path: "dayjs.min.js"
    },
    {
      name: "echarts",
      var: "echarts",
      path: "echarts.min.js"
    }
  ]
});
