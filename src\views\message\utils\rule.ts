import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const messageRules = reactive<FormRules>({
  chatId: [
    {
      required: true,
      message: $t("Please select a chat"),
      trigger: "change"
    }
  ],
  role: [
    {
      required: true,
      message: $t("Please select message role"),
      trigger: "change"
    }
  ],
  content: [
    {
      required: true,
      message: $t("Please enter message content"),
      trigger: "blur"
    },
    {
      min: 1,
      max: 10000,
      message: $t("Length must be between 1 and 10000 characters"),
      trigger: "blur"
    }
  ],
  contentType: [
    {
      required: true,
      message: $t("Please select content type"),
      trigger: "change"
    }
  ]
});

export { messageRules };
