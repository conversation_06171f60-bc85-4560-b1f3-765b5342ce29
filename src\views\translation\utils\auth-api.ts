import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/translation/utils/type";

export const getTranslations = (params?: object) => {
  return http.request<Result>("get", "/api/auth/translations", {
    params
  });
};

export const getTranslationById = (id: number) => {
  return http.request<Result>("get", `/api/auth/translations/${id}`);
};

export const createTranslation = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/translations", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateTranslationById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/translations/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const deleteTranslationById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/translations/${id}`);
};

export const bulkDeleteTranslations = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/translations/bulk-delete", {
    data
  });
};

export const destroyTranslationById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/translations/${id}`);
};

export const bulkDestroyTranslations = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/translations/bulk-destroy", {
    data
  });
};

export const restoreTranslationById = (id: number) => {
  return http.request<Result>("put", `/api/auth/translations/${id}/restore`);
};

export const bulkRestoreTranslations = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/translations/bulk-restore", {
    data
  });
};

export const dropdownTranslations = () => {
  return http.request<Result>("get", "/api/auth/translations/dropdown");
};

export const getTranslationGroups = () => {
  return http.request<Result>("get", "/api/auth/translations/groups");
};

export const syncTranslations = (data: { languageCode: string }) => {
  return http.request<Result>("post", "/api/auth/translations/sync", {
    data
  });
};

export const exportTranslations = (params?: object) => {
  return http.request<Result>("get", "/api/auth/translations/export", {
    params,
    responseType: "blob"
  });
};

export const importTranslations = (data: FormData) => {
  return http.request<Result>("post", "/api/auth/translations/import", {
    data,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
