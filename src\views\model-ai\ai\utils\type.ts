export type FormItemProps = {
  id?: number | null;
  name?: string;
  key?: string;
  modelProviderId?: number;
  version?: string;
  apiEndpoint?: string;
  streaming?: boolean;
  vision?: boolean;
  functionCalling?: boolean;
  isDefault?: boolean;
  description?: string;
  sortOrder?: number;
  status?: string;
};

export type ModelAiFilterProps = {
  name?: string;
  key?: string;
  modelProviderId?: number | null;
  streaming?: boolean;
  status?: string;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
