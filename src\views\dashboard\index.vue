<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent } from "vue";
import { $t } from "@/plugins/i18n";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getDashboardStats,
  getBotStats,
  getChatStats,
  getMessageStats,
  getUserStats
} from "./utils/api";

// Lazy load components
const StatsCard = defineAsyncComponent(
  () => import("./components/StatsCard.vue")
);
const RecentActivities = defineAsyncComponent(
  () => import("./components/RecentActivities.vue")
);

// State
const loading = ref(false);
const stats = ref({
  totalBots: 0,
  totalChats: 0,
  totalMessages: 0,
  totalUsers: 0,
  activeBots: 0,
  activeChats: 0,
  todayMessages: 0,
  onlineUsers: 0
});

const trends = ref({
  bots: { value: 0, isUp: true },
  chats: { value: 0, isUp: true },
  messages: { value: 0, isUp: true },
  users: { value: 0, isUp: true }
});

// Methods
const loadDashboardData = async () => {
  loading.value = true;
  try {
    // Mock data for demonstration
    stats.value = {
      totalBots: 25,
      totalChats: 1250,
      totalMessages: 15680,
      totalUsers: 342,
      activeBots: 18,
      activeChats: 89,
      todayMessages: 456,
      onlineUsers: 23
    };

    trends.value = {
      bots: { value: 12.5, isUp: true },
      chats: { value: 8.3, isUp: true },
      messages: { value: 15.7, isUp: true },
      users: { value: 5.2, isUp: false }
    };
  } catch (error) {
    console.error("Error loading dashboard data:", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadDashboardData();
});
</script>

<template>
  <div class="dashboard">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-2">
        {{ $t("Dashboard Overview") }}
      </h1>
      <p class="text-gray-600">
        {{
          $t(
            "Welcome back! Here's what's happening with your AI assistant platform."
          )
        }}
      </p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        :title="$t('Total Bots')"
        :value="stats.totalBots"
        icon="ri/robot-2-line"
        color="primary"
        :trend="trends.bots"
        :loading="loading"
      />
      <StatsCard
        :title="$t('Total Chats')"
        :value="stats.totalChats"
        icon="ri/chat-3-line"
        color="success"
        :trend="trends.chats"
        :loading="loading"
      />
      <StatsCard
        :title="$t('Total Messages')"
        :value="stats.totalMessages"
        icon="ri/message-3-line"
        color="warning"
        :trend="trends.messages"
        :loading="loading"
      />
      <StatsCard
        :title="$t('Total Users')"
        :value="stats.totalUsers"
        icon="ri/user-line"
        color="info"
        :trend="trends.users"
        :loading="loading"
      />
    </div>

    <!-- Secondary Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        :title="$t('Active Bots')"
        :value="stats.activeBots"
        icon="ri/robot-line"
        color="success"
        :loading="loading"
      />
      <StatsCard
        :title="$t('Active Chats')"
        :value="stats.activeChats"
        icon="ri/chat-1-line"
        color="primary"
        :loading="loading"
      />
      <StatsCard
        :title="$t('Today Messages')"
        :value="stats.todayMessages"
        icon="ri/message-2-line"
        color="warning"
        :loading="loading"
      />
      <StatsCard
        :title="$t('Online Users')"
        :value="stats.onlineUsers"
        icon="ri/user-3-line"
        color="success"
        :loading="loading"
      />
    </div>

    <!-- Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Recent Activities -->
      <div class="lg:col-span-2">
        <RecentActivities />
      </div>

      <!-- Quick Actions -->
      <div>
        <el-card shadow="hover">
          <template #header>
            <span class="text-lg font-semibold">{{ $t("Quick Actions") }}</span>
          </template>

          <div class="space-y-3">
            <el-button
              type="primary"
              class="w-full"
              @click="$router.push('/bots')"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ep:plus')"
                class="mr-2"
              />
              {{ $t("Create New Bot") }}
            </el-button>

            <el-button
              type="success"
              class="w-full"
              @click="$router.push('/chat/management')"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ri/chat-new-line')"
                class="mr-2"
              />
              {{ $t("Start New Chat") }}
            </el-button>

            <el-button
              type="warning"
              class="w-full"
              @click="$router.push('/user/management')"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ri/user-add-line')"
                class="mr-2"
              />
              {{ $t("Manage Users") }}
            </el-button>

            <el-button
              type="info"
              class="w-full"
              @click="$router.push('/message/management')"
            >
              <IconifyIconOffline
                :icon="useRenderIcon('ri/message-line')"
                class="mr-2"
              />
              {{ $t("View Messages") }}
            </el-button>
          </div>
        </el-card>

        <!-- System Status -->
        <el-card shadow="hover" class="mt-6">
          <template #header>
            <span class="text-lg font-semibold">{{ $t("System Status") }}</span>
          </template>

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">{{ $t("API Status") }}</span>
              <el-tag type="success" size="small">{{ $t("Online") }}</el-tag>
            </div>

            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">{{ $t("Database") }}</span>
              <el-tag type="success" size="small">{{ $t("Connected") }}</el-tag>
            </div>

            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">{{ $t("AI Models") }}</span>
              <el-tag type="success" size="small">{{ $t("Available") }}</el-tag>
            </div>

            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">{{ $t("Storage") }}</span>
              <el-tag type="warning" size="small">{{ $t("75% Used") }}</el-tag>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

:deep(.el-card) {
  border-radius: 8px;
}

:deep(.el-card__header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
