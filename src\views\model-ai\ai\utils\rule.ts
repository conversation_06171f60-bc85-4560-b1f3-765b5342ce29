import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const modelAiRules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: $t("Please enter the model name"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 100,
      message: $t("Length must be between 2 and 100 characters"),
      trigger: "blur"
    }
  ],
  key: [
    {
      required: true,
      message: $t("Please enter the model key"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 100,
      message: $t("Length must be between 2 and 100 characters"),
      trigger: "blur"
    },
    {
      pattern: /^[a-z0-9-_]+$/,
      message: $t(
        "Key can only contain lowercase letters, numbers, hyphens and underscores"
      ),
      trigger: "blur"
    }
  ]
});

export { modelAiRules };
