import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";
import { capitalized } from "@/utils/helpers";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "key",
    align: "left",
    sortable: true,
    width: 140,
    headerRenderer: () => $t("Provider code")
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    width: 140,
    headerRenderer: () => $t("Provider name")
  },
  {
    prop: "description",
    align: "left",
    minWidth: 210,
    className: "text-ellipsis",
    headerRenderer: () => $t("Provider name")
  },
  {
    prop: "status",
    sortable: true,
    align: "center",
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.status === "active" ? "success" : "danger",
          size: "small"
        },
        () => $t(capitalized(row.status)).toUpperCase()
      );
    }
  },
  {
    prop: "createdAt",
    width: 160,
    sortable: true,
    headerRenderer: () => $t("Created at"),
    formatter: ({ createdAt }) =>
      createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
