// Test file to debug authentication issues
console.log("=== Authentication Debug Test ===");

// Test data conversion
const testApiResponse = {
  "success": true,
  "message": "Login successful.",
  "data": {
    "user": {
      "username": "admin",
      "first_name": "<PERSON><PERSON><PERSON>",
      "last_name": "A", 
      "full_name": "<PERSON>uy<PERSON>",
      "avatar": null,
      "birthday": null,
      "gender": "other",
      "email": "<EMAIL>",
      "email_verified_at": "2025-06-23T09:05:18.000000Z",
      "phone": null,
      "phone_verified_at": null,
      "address": null,
      "status": "active",
      "last_login_at": "2025-06-23T09:09:07.000000Z",
      "last_login_ip": "127.0.0.1",
      "preferences": null,
      "is_verified": true,
      "newsletter_subscribed": false,
      "created_at": "2025-06-23T09:02:27.000000Z",
      "updated_at": "2025-06-23T09:09:07.000000Z",
      "permissions": ["*:*:*"],
      "roles": ["admin"],
      "geo_division": null,
      "country": null
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
    "token_type": "Bearer",
    "expires_in": 3600
  }
};

// Test camelCase conversion
function convertSnakeToCamel(obj) {
  if (!obj) return obj;

  const convertSnakeToCamelKey = (key) =>
    key.replace(/_([a-z])/g, (_, char) => char.toUpperCase());

  const convertObjectKeys = (o) => {
    if (Array.isArray(o)) {
      return o.map(item => convertObjectKeys(item));
    } else if (o !== null && o !== undefined && o.constructor === Object) {
      return Object.keys(o).reduce((acc, key) => {
        const camelKey = convertSnakeToCamelKey(key);
        acc[camelKey] = convertObjectKeys(o[key]);
        return acc;
      }, {});
    }
    return o;
  };

  return convertObjectKeys(obj);
}

console.log("Original API Response:");
console.log(JSON.stringify(testApiResponse, null, 2));

console.log("\nConverted to camelCase:");
const convertedResponse = convertSnakeToCamel(testApiResponse);
console.log(JSON.stringify(convertedResponse, null, 2));

// Test token expiry calculation
const now = Date.now();
const expiresIn = convertedResponse.data.expiresIn; // 3600 seconds
const expires = now + (expiresIn * 1000);

console.log("\nToken Expiry Test:");
console.log("Current time:", now);
console.log("Expires in seconds:", expiresIn);
console.log("Expires timestamp:", expires);
console.log("Expires date:", new Date(expires));
console.log("Time until expiry (minutes):", (expires - now) / 60000);

// Test cookie expires calculation
const cookieExpires = (expires - Date.now()) / 86400000;
console.log("Cookie expires (days):", cookieExpires);
console.log("Cookie expires (hours):", cookieExpires * 24);

// Test user data extraction
const user = convertedResponse.data.user;
console.log("\nUser Data Extraction:");
console.log("Username:", user.username);
console.log("Full Name:", user.fullName);
console.log("First + Last:", user.firstName + " " + user.lastName);
console.log("Roles:", user.roles);
console.log("Permissions:", user.permissions);

// Test token format
const token = convertedResponse.data.token;
const formattedToken = "Bearer " + token;
console.log("\nToken Format:");
console.log("Original token:", token);
console.log("Formatted token:", formattedToken);

console.log("\n=== Debug Test Complete ===");
