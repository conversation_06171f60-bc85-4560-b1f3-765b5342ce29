import { reactive, ref } from "vue";
import {
  getPages,
  createPage,
  updatePageById,
  deletePageById,
  bulkDeletePages,
  bulkDestroyPages,
  destroyPageById,
  restorePageById,
  bulkRestorePages
} from "../utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { PageFilterProps } from "@/views/page/utils/type";

export function usePageHook() {
  // Data/State
  const loading = ref(false);
  const filterRef = ref<PageFilterProps>({});
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "title", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({ status: "draft" });
  const pageFormRef = ref();

  // API Handlers
  const fnGetPages = async () => {
    loading.value = true;
    try {
      const response = await getPages({
        ...filterRef.value,
        order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
        page: pagination.currentPage,
        limit: pagination.pageSize
      });
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;
    } catch (e) {
      console.error("Get Pages error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  const fnHandleCreatePage = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createPage(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetPages();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const fnHandleUpdatePage = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updatePageById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetPages();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetPages();
  };

  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetPages();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetPages();
  };

  // Delete/Destroy/Restore Handlers
  const fnHandleDelete = async (id: number) => {
    try {
      const response = await deletePageById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetPages();
        return true;
      }
      message(response?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    }
  };

  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      const response = await bulkDeletePages({ ids });
      if (response.success) {
        message(response.message || $t("Bulk delete successful"), {
          type: "success"
        });
        await fnGetPages();
        return true;
      }
      message(response?.message || $t("Bulk delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Bulk delete failed"),
        {
          type: "error"
        }
      );
      return false;
    }
  };

  // UI Action Handlers
  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(row.id);
    } catch (error) {
      if (error !== "cancel") {
        console.error("Delete error:", error);
      }
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await fnHandleBulkDelete(ids);
    } catch (error) {
      if (error !== "cancel") {
        console.error("Bulk delete error:", error);
      }
    }
  };

  const handleDestroy = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to move this item to trash?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const response = await destroyPageById(row.id);
      if (response.success) {
        message(response.message || $t("Destroy successful"), {
          type: "success"
        });
        await fnGetPages();
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Destroy failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleBulkDestroy = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to destroy"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to move selected items to trash?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDestroyPages({ ids });
      if (response.success) {
        message(response.message || $t("Bulk destroy successful"), {
          type: "success"
        });
        await fnGetPages();
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Bulk destroy failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleRestore = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this item?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );
      const response = await restorePageById(row.id);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetPages();
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Restore failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected items?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkRestorePages({ ids });
      if (response.success) {
        message(response.message || $t("Bulk restore successful"), {
          type: "success"
        });
        await fnGetPages();
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Bulk restore failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  // Form Handlers
  const handleSubmit = async (values: FieldValues) => {
    if (values.id != null) {
      await fnHandleUpdatePage(Number(values.id), values);
      return;
    }
    const success = await fnHandleCreatePage(values);
    if (success) {
      drawerValues.value = { status: "draft" };
      pageFormRef.value?.resetForm();
    }
  };

  const handleFilter = async (values: PageFilterProps) => {
    filterRef.value = values;
    await fnGetPages();
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    pageFormRef,

    // API Handlers
    fnGetPages,
    fnHandleCreatePage,
    fnHandleUpdatePage,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
