// Test token refresh logic with real login response
console.log("=== Token Refresh Logic Test ===");

// Simulate real login response
const loginResponse = {
  "success": true,
  "message": "Login successful.",
  "data": {
    "user": {
      "username": "admin",
      "first_name": "<PERSON><PERSON><PERSON>",
      "last_name": "A",
      "full_name": "<PERSON>uy<PERSON>",
      "avatar": null,
      "birthday": null,
      "gender": "other",
      "email": "<EMAIL>",
      "email_verified_at": "2025-06-23T09:05:18.000000Z",
      "phone": null,
      "phone_verified_at": null,
      "address": null,
      "status": "active",
      "last_login_at": "2025-06-23T09:09:07.000000Z",
      "last_login_ip": "127.0.0.1",
      "preferences": null,
      "is_verified": true,
      "newsletter_subscribed": false,
      "created_at": "2025-06-23T09:02:27.000000Z",
      "updated_at": "2025-06-23T09:09:07.000000Z",
      "permissions": ["*:*:*"],
      "roles": ["admin"],
      "geo_division": null,
      "country": null
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
    "token_type": "Bearer",
    "expires_in": 60 // 1 minute for testing
  }
};

// Convert to camelCase as in app
function convertSnakeToCamel(obj) {
  if (!obj) return obj;
  const convertKey = (key) => key.replace(/_([a-z])/g, (_, char) => char.toUpperCase());
  const convertObjectKeys = (o) => {
    if (Array.isArray(o)) {
      return o.map(item => convertObjectKeys(item));
    } else if (o !== null && o !== undefined && o.constructor === Object) {
      return Object.keys(o).reduce((acc, key) => {
        const camelKey = convertKey(key);
        acc[camelKey] = convertObjectKeys(o[key]);
        return acc;
      }, {});
    }
    return o;
  };
  return convertObjectKeys(obj);
}

const convertedData = convertSnakeToCamel(loginResponse.data);
console.log("Converted login data:", JSON.stringify(convertedData, null, 2));

// Simulate setToken logic
const now = Date.now();
const accessToken = convertedData.token;
const refreshToken = convertedData.token; // Same token as refresh
const expires = now + (convertedData.expiresIn * 1000);

console.log("\n=== Token Storage Simulation ===");
console.log("Current time:", new Date(now).toISOString());
console.log("Expires in seconds:", convertedData.expiresIn);
console.log("Expires timestamp:", expires);
console.log("Expires date:", new Date(expires).toISOString());
console.log("Time until expiry (minutes):", (expires - now) / 60000);

// Simulate cookie storage
const cookieString = JSON.stringify({ accessToken, expires, refreshToken });
const cookieExpires = Math.max((expires - Date.now()) / 86400000, 0.001);
console.log("Cookie expires (days):", cookieExpires);
console.log("Cookie expires (hours):", cookieExpires * 24);

// Simulate token check logic (as in HTTP interceptor)
function checkTokenExpiry(tokenData) {
  const now = new Date().getTime();
  const tokenExpires = typeof tokenData.expires === 'number' ? tokenData.expires : parseInt(tokenData.expires);
  const expired = tokenExpires - now <= 0;
  
  console.log("\n=== Token Expiry Check ===");
  console.log("Current time:", new Date(now).toISOString());
  console.log("Token expires:", new Date(tokenExpires).toISOString());
  console.log("Time remaining (seconds):", (tokenExpires - now) / 1000);
  console.log("Is expired:", expired);
  
  return expired;
}

// Test ngay sau khi login
const tokenData = { accessToken, expires, refreshToken };
console.log("\n=== Immediate Check (Right after login) ===");
checkTokenExpiry(tokenData);

// Test after 30 seconds
console.log("\n=== Check after 30 seconds ===");
const futureTime30s = now + 30000;
const futureTokenData30s = { ...tokenData, currentTime: futureTime30s };
console.log("Simulated time:", new Date(futureTime30s).toISOString());
const expired30s = expires - futureTime30s <= 0;
console.log("Would be expired:", expired30s);
console.log("Time remaining (seconds):", (expires - futureTime30s) / 1000);

// Test after 65 seconds (should be expired)
console.log("\n=== Check after 65 seconds (should be expired) ===");
const futureTime65s = now + 65000;
console.log("Simulated time:", new Date(futureTime65s).toISOString());
const expired65s = expires - futureTime65s <= 0;
console.log("Would be expired:", expired65s);
console.log("Time remaining (seconds):", (expires - futureTime65s) / 1000);

// Test refresh token response
const refreshResponse = {
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzUxMiJ9.newAdmin",
    "refreshToken": "eyJhbGciOiJIUzUxMiJ9.newAdminRefresh",
    "expires": "2030/10/30 23:59:59"
  }
};

console.log("\n=== Refresh Token Response ===");
console.log("Refresh response:", JSON.stringify(refreshResponse, null, 2));

// Parse refresh token expires
const refreshExpires = new Date(refreshResponse.data.expires).getTime();
console.log("New expires timestamp:", refreshExpires);
console.log("New expires date:", new Date(refreshExpires).toISOString());
console.log("New token valid for (hours):", (refreshExpires - Date.now()) / (1000 * 60 * 60));

console.log("\n=== Test Complete ===");
