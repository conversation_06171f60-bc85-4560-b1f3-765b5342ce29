import { reactive, ref } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import {
  getSpaces,
  deleteSpaceById,
  bulkDeleteSpaces,
  destroySpaceByUuid,
  bulkDestroySpaces,
  restoreSpaceById,
  bulkRestoreSpaces,
  createSpace,
  updateSpaceByUuid
} from "./auth-api";
import type { SpaceFilterProps } from "@/views/space/utils/type";

export function useSpaceHook() {
  // Data/State
  const loading = ref(false);
  const filterRef = ref<SpaceFilterProps>({});
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<object>({
    name: null,
    email: null,
    logo: null,
    logoFile: null,
    description: null,
    phone: null
  });
  const spaceFormRef = ref();

  const fnGetSpaces = async () => {
    loading.value = true;
    try {
      const response = await getSpaces(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;

      console.log("Re vale***************>", records.value);
    } catch (e) {
      console.error("Get Space error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = async ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    await fnGetSpaces();
  };

  const fnHandlePageChange = async (val: number) => {
    pagination.currentPage = val;
    await fnGetSpaces();
  };

  const fnHandleSizeChange = async (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    await fnGetSpaces();
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(id);
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleDelete = async (id: number) => {
    try {
      loading.value = true;
      const response = await deleteSpaceById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetSpaces();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const handleBulkDelete = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkDelete(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteSpaces({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetSpaces();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk delete Categories error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Destroy handlers and actions
   ***************************
   */
  const handleDestroy = async (uuid: string) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDestroy(uuid);
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleDestroy = async (uuid: string) => {
    try {
      loading.value = true;
      const response = await destroySpaceByUuid(uuid);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetSpaces();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const handleBulkDestroy = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkDestroy(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleBulkDestroy = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDestroySpaces({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetSpaces();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk delete Categories error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Restore handlers and actions
   ***************************
   */
  const handleRestore = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleRestore(id);
    } catch {
      console.log("Restore cancelled");
    }
  };
  const fnHandleRestore = async (id: number) => {
    try {
      loading.value = true;
      const response = await restoreSpaceById(id);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetSpaces();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message ||
          error?.message ||
          $t("Restores failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const handleBulkRestore = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to restore"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkRestore(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Restore cancelled");
    }
  };
  const fnHandleBulkRestore = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkRestoreSpaces({ ids });
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetSpaces();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk restore Categories error:", error);
      message(error.response?.data?.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   CTA handlers
   ***************************
   */
  const handleSubmit = async (values: object) => {
    // @ts-ignore
    if (values?.uuid) {
      console.log("Updating space with data:", values);
      // @ts-ignore
      await fnHandleUpdateSpace(values.uuid, values);
      return;
    }
    const success = await fnHandleCreateSpace(values);
    if (success) {
      // Close drawer first
      drawerVisible.value = false;

      // Reset form
      spaceFormRef.value?.resetForm();
      drawerValues.value = {
        name: null,
        email: null,
        logo: null,
        logoFile: null,
        description: null,
        phone: null
      };
    }
  };
  const handleFilter = async (values: SpaceFilterProps) => {
    filterRef.value = values;
    await fnGetSpaces();
  };
  const fnHandleCreateSpace = async (formData: object) => {
    try {
      loading.value = true;
      console.log("Creating space with data:", formData);
      const response = await createSpace(formData);
      console.log("Create space response:", response);

      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        console.log("Space created successfully, refreshing list...");
        await fnGetSpaces();
        console.log("Space list refreshed");
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Create space error:", error);
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const fnHandleUpdateSpace = async (uuid: string, formData: object) => {
    try {
      loading.value = true;
      const response = await updateSpaceByUuid(uuid, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetSpaces();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    spaceFormRef,

    // API Handlers
    fnGetSpaces,
    fnHandleCreateSpace,
    fnHandleUpdateSpace,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
