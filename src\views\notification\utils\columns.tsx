import { $t } from "@/plugins/i18n";
import { hasAuth } from "@/router/utils";

function getTypeColor(type: string): string {
  const colorMap = {
    info: "bg-blue-500",
    success: "bg-green-500",
    warning: "bg-yellow-500",
    error: "bg-red-500",
    announcement: "bg-purple-500"
  };
  return colorMap[type] || colorMap.info;
}

export function columns() {
  return [
    {
      label: $t("Title"),
      prop: "title",
      minWidth: 200,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <div class="flex items-center space-x-2">
          <div class={`w-2 h-2 rounded-full ${getTypeColor(row.type)}`}></div>
          <span class={row.isRead ? "text-gray-500" : "font-semibold"}>
            {row.title}
          </span>
          {row.priority === "urgent" && (
            <el-tag type="danger" size="small">
              {$t("Urgent")}
            </el-tag>
          )}
          {row.priority === "high" && (
            <el-tag type="warning" size="small">
              {$t("High")}
            </el-tag>
          )}
        </div>
      )
    },
    {
      label: $t("Type"),
      prop: "type",
      width: 120,
      cellRenderer: ({ row }) => {
        const typeMap = {
          info: { type: "info", text: $t("Info") },
          success: { type: "success", text: $t("Success") },
          warning: { type: "warning", text: $t("Warning") },
          error: { type: "danger", text: $t("Error") },
          announcement: { type: "primary", text: $t("Announcement") }
        };
        const config = typeMap[row.type] || typeMap.info;
        return <el-tag type={config.type} size="small">{config.text}</el-tag>;
      }
    },
    {
      label: $t("Priority"),
      prop: "priority",
      width: 100,
      cellRenderer: ({ row }) => {
        const priorityMap = {
          low: { type: "info", text: $t("Low") },
          medium: { type: "primary", text: $t("Medium") },
          high: { type: "warning", text: $t("High") },
          urgent: { type: "danger", text: $t("Urgent") }
        };
        const config = priorityMap[row.priority] || priorityMap.medium;
        return <el-tag type={config.type} size="small">{config.text}</el-tag>;
      }
    },
    {
      label: $t("Target"),
      prop: "targetType",
      width: 120,
      cellRenderer: ({ row }) => {
        const targetMap = {
          all: { icon: "ep:user", text: $t("All Users") },
          role: { icon: "ep:user-filled", text: $t("Role") },
          user: { icon: "ep:avatar", text: $t("User") },
          group: { icon: "ep:users", text: $t("Group") }
        };
        const config = targetMap[row.targetType] || targetMap.all;
        return (
          <div class="flex items-center space-x-1">
            <iconify-icon-online icon={config.icon} width="14px" />
            <span>{config.text}</span>
          </div>
        );
      }
    },
    {
      label: $t("Status"),
      prop: "status",
      width: 120,
      cellRenderer: ({ row }) => {
        const statusMap = {
          draft: { type: "info", text: $t("Draft") },
          scheduled: { type: "warning", text: $t("Scheduled") },
          sent: { type: "success", text: $t("Sent") },
          expired: { type: "danger", text: $t("Expired") },
          cancelled: { type: "info", text: $t("Cancelled") }
        };
        const config = statusMap[row.status] || statusMap.draft;
        return <el-tag type={config.type} size="small">{config.text}</el-tag>;
      }
    },
    {
      label: $t("Recipients"),
      prop: "recipientCount",
      width: 100,
      align: "center",
      cellRenderer: ({ row }) => (
        <div class="text-center">
          <div class="text-sm font-medium">{row.recipientCount || 0}</div>
          {row.readCount !== undefined && (
            <div class="text-xs text-gray-500">
              {row.readCount} {$t("read")}
            </div>
          )}
        </div>
      )
    },
    {
      label: $t("Scheduled At"),
      prop: "scheduledAt",
      width: 160,
      cellRenderer: ({ row }) => {
        if (!row.scheduledAt) return "-";
        const date = new Date(row.scheduledAt);
        return (
          <div class="text-sm">
            <div>{date.toLocaleDateString()}</div>
            <div class="text-gray-500">{date.toLocaleTimeString()}</div>
          </div>
        );
      }
    },
    {
      label: $t("Created At"),
      prop: "createdAt",
      width: 160,
      cellRenderer: ({ row }) => {
        const date = new Date(row.createdAt);
        return (
          <div class="text-sm">
            <div>{date.toLocaleDateString()}</div>
            <div class="text-gray-500">{date.toLocaleTimeString()}</div>
          </div>
        );
      }
    },
    {
      label: $t("Action"),
      fixed: "right",
      width: 200,
      slot: "operation"
    }
  ];
}


