import {
  type RouterHistory,
  type RouteRecordRaw,
  type RouteComponent,
  createWebHistory,
  createWebHashHistory
} from "vue-router";
import { router } from "./index";
import { isProxy, toRaw } from "vue";
import { useTimeoutFn } from "@vueuse/core";
import {
  isString,
  cloneDeep,
  isAllEmpty,
  intersection,
  storageLocal,
  isIncludeAllChildren
} from "@pureadmin/utils";
import { getConfig } from "@/config";
import { buildHierarchyTree } from "@/utils/tree";
import { userKey, type DataInfo } from "@/utils/auth";
import { type menuType, routerArrays } from "@/layout/types";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { usePermissionStoreHook } from "@/store/modules/permission";
const IFrame = () => import("@/layout/frame.vue");
// https://cn.vitejs.dev/guide/features.html#glob-import
const modulesRoutes = import.meta.glob("/src/views/**/*.{vue,tsx}");

// Dynamic routing
import { getAsyncRoutes } from "@/api/routes";

function handRank(routeInfo: any) {
  const { name, path, parentId, meta } = routeInfo;
  if (isAllEmpty(parentId)) {
    return (
      isAllEmpty(meta?.rank) ||
      (meta?.rank === 0 && name !== "Home" && path !== "/")
    );
  } else return false;
}

/** Sorts routes in ascending order based on the 'rank' in their meta property. */
function ascending(arr: any[]) {
  arr.forEach((v, index) => {
    // When rank doesn't exist, it's created automatically in order. The homepage route is always first.
    if (handRank(v)) v.meta.rank = index + 2;
  });
  return arr.sort(
    (a: { meta: { rank: number } }, b: { meta: { rank: number } }) => {
      return a?.meta.rank - b?.meta.rank;
    }
  );
}

/** Filters out menu items where meta.showLink is false. */
function filterTree(data: RouteComponent[]) {
  const newTree = cloneDeep(data).filter(
    (v: { meta: { showLink: boolean } }) => v.meta?.showLink !== false
  );
  newTree.forEach(
    (v: { children: any }) =>
      v.children && (v.children = filterTree(v.children))
  );
  return newTree;
}

/** Filters out directories with no children. If a directory has no visible menu items (after permission filtering), it will be removed. */
function filterChildrenTree(data: RouteComponent[]) {
  const newTree = cloneDeep(data).filter((v: any) => v?.children?.length !== 0);
  newTree.forEach(
    (v: { children: any }) =>
      v.children && (v.children = filterTree(v.children))
  );
  return newTree;
}

/** Checks if two arrays have any common values. */
function isOneOfArray(a: Array<string>, b: Array<string>) {
  return Array.isArray(a) && Array.isArray(b)
    ? intersection(a, b).length > 0
    : true;
}

/** Fetches the current user's roles from localStorage and filters out menus they don't have permission for. */
function filterNoPermissionTree(data: RouteComponent[]) {
  const currentRoles =
    storageLocal().getItem<DataInfo<number>>(userKey)?.roles ?? [];
  const newTree = cloneDeep(data).filter((v: any) =>
    isOneOfArray(v.meta?.roles, currentRoles)
  );
  newTree.forEach(
    (v: any) => v.children && (v.children = filterNoPermissionTree(v.children))
  );
  return filterChildrenTree(newTree);
}

/**
 * Gets the collection of parent paths by a specified `key`, defaulting to `path`.
 * @param value The value to find.
 * @param routes The list of routes to search in.
 * @param key The key to match against (e.g., 'path', 'name').
 */
function getParentPaths(value: string, routes: RouteRecordRaw[], key = "path") {
  // Depth-first search
  function dfs(routes: RouteRecordRaw[], value: string, parents: string[]) {
    for (let i = 0; i < routes.length; i++) {
      const item = routes[i];
      // Return parent paths
      if (item[key] === value) return parents;
      // If children don't exist or are empty, do not recurse
      if (!item.children || !item.children.length) continue;
      // When searching downwards, push the current path onto the stack
      parents.push(item.path);

      if (dfs(item.children, value, parents).length) return parents;
      // If not found during DFS, pop the current path from the stack
      parents.pop();
    }
    // If not found, return an empty array
    return [];
  }

  return dfs(routes, value, []);
}

/** Finds route information for a given `path`. */
function findRouteByPath(path: string, routes: RouteRecordRaw[]) {
  let res = routes.find((item: { path: string }) => item.path == path);
  if (res) {
    return isProxy(res) ? toRaw(res) : res;
  } else {
    for (let i = 0; i < routes.length; i++) {
      if (
        routes[i].children instanceof Array &&
        routes[i].children.length > 0
      ) {
        res = findRouteByPath(path, routes[i].children);
        if (res) {
          return isProxy(res) ? toRaw(res) : res;
        }
      }
    }
    return null;
  }
}

function addPathMatch() {
  if (!router.hasRoute("pathMatch")) {
    router.addRoute({
      path: "/:pathMatch(.*)",
      name: "pathMatch",
      redirect: "/error/404"
    });
  }
}

/** Processes dynamic routes (routes returned from the backend). */
function handleAsyncRoutes(routeList: any) {
  if (routeList.length === 0) {
    usePermissionStoreHook().handleWholeMenus(routeList);
  } else {
    formatFlatteningRoutes(addAsyncRoutes(routeList)).map(
      (v: RouteRecordRaw) => {
        // Prevent duplicate route additions
        if (
          router.options.routes[0].children.findIndex(
            value => value.path === v.path
          ) !== -1
        ) {
          return;
        } else {
          // Remember to use addRoute after pushing the route to routes for it to work correctly
          router.options.routes[0].children.push(v);
          // Finally, sort the routes in ascending order
          ascending(router.options.routes[0].children);
          if (!router.hasRoute(v?.name)) router.addRoute(v);
          const flattenRouters: any = router
            .getRoutes()
            .find(n => n.path === "/");
          // Keep the children of router.options.routes[0] and the children of the "/" path consistent to prevent data inconsistency issues
          flattenRouters.children = router.options.routes[0].children;
          router.addRoute(flattenRouters);
        }
      }
    );
    usePermissionStoreHook().handleWholeMenus(routeList);
  }
  if (!useMultiTagsStoreHook().getMultiTagsCache) {
    useMultiTagsStoreHook().handleTags("equal", [
      ...routerArrays,
      ...usePermissionStoreHook().flatteningRoutes.filter(
        v => v?.meta?.fixedTag
      )
    ]);
  }
  addPathMatch();
}

/** Initializes the router (using `new Promise` to prevent infinite loops in async requests). */
function initRouter() {
  // Temporarily disable dynamic routes loading for testing
  return new Promise(resolve => {
    console.log("Dynamic routes disabled for testing - using static routes only");
    handleAsyncRoutes([]);
    resolve(router);
  });

  /* Original dynamic routes code - uncomment when server endpoint is ready
  if (getConfig()?.CachingAsyncRoutes) {
    // Enable caching of dynamic routes in local localStorage
    const key = "async-routes";
    const asyncRouteList = storageLocal().getItem(key) as any;
    if (asyncRouteList && asyncRouteList?.length > 0) {
      return new Promise(resolve => {
        handleAsyncRoutes(asyncRouteList);
        resolve(router);
      });
    } else {
      return new Promise(resolve => {
        getAsyncRoutes()
          .then(({ data }) => {
            handleAsyncRoutes(cloneDeep(data));
            storageLocal().setItem(key, data);
            resolve(router);
          })
          .catch(error => {
            console.warn("Failed to load async routes:", error);
            // Fallback to empty routes if API fails
            handleAsyncRoutes([]);
            resolve(router);
          });
      });
    }
  } else {
    return new Promise(resolve => {
      getAsyncRoutes()
        .then(({ data }) => {
          handleAsyncRoutes(cloneDeep(data));
          resolve(router);
        })
        .catch(error => {
          console.warn("Failed to load async routes:", error);
          // Fallback to empty routes if API fails
          handleAsyncRoutes([]);
          resolve(router);
        });
    });
  }
  */
}

/**
 * Flattens a multi-level nested route list into a one-dimensional array.
 * @param routesList The routes to process.
 * @returns Returns the flattened one-dimensional routes.
 */
function formatFlatteningRoutes(routesList: RouteRecordRaw[]) {
  if (routesList.length === 0) return routesList;
  let hierarchyList = buildHierarchyTree(routesList);
  for (let i = 0; i < hierarchyList.length; i++) {
    if (hierarchyList[i].children) {
      hierarchyList = hierarchyList
        .slice(0, i + 1)
        .concat(hierarchyList[i].children, hierarchyList.slice(i + 1));
    }
  }
  return hierarchyList;
}

/**
 * Converts a one-dimensional array into a two-stage nested array.
 * (Routes with 3+ levels are flattened to 2 levels, as keep-alive only supports two levels).
 * See: https://github.com/pure-admin/vue-pure-admin/issues/67
 * @param routesList The flattened one-dimensional route array.
 * @returns Returns the one-dimensional array reformatted into the specified route structure.
 */
function formatTwoStageRoutes(routesList: RouteRecordRaw[]) {
  if (routesList.length === 0) return routesList;
  const newRoutesList: RouteRecordRaw[] = [];
  routesList.forEach((v: RouteRecordRaw) => {
    if (v.path === "/") {
      newRoutesList.push({
        component: v.component,
        name: v.name,
        path: v.path,
        redirect: v.redirect,
        meta: v.meta,
        children: []
      });
    } else {
      newRoutesList[0]?.children.push({ ...v });
    }
  });
  return newRoutesList;
}

/** Handles cached routes (add, delete, refresh) for keep-alive. */
function handleAliveRoute({ name }: ToRouteType, mode?: string) {
  switch (mode) {
    case "add":
      usePermissionStoreHook().cacheOperate({
        mode: "add",
        name
      });
      break;
    case "delete":
      usePermissionStoreHook().cacheOperate({
        mode: "delete",
        name
      });
      break;
    case "refresh":
      usePermissionStoreHook().cacheOperate({
        mode: "refresh",
        name
      });
      break;
    default:
      usePermissionStoreHook().cacheOperate({
        mode: "delete",
        name
      });
      useTimeoutFn(() => {
        usePermissionStoreHook().cacheOperate({
          mode: "add",
          name
        });
      }, 100);
  }
}

/** Filters dynamic routes from the backend and regenerates them into a standardized format. */
function addAsyncRoutes(arrRoutes: Array<RouteRecordRaw>) {
  if (!arrRoutes || !arrRoutes.length) return;
  const modulesRoutesKeys = Object.keys(modulesRoutes);
  arrRoutes.forEach((v: RouteRecordRaw) => {
    // Add the 'backstage' property to meta to identify this as a backend-returned route
    if (v.meta) {
      v.meta.backstage = true;
    }
    // Parent's redirect value: If children exist and the parent's redirect doesn't, it defaults to the first child's path. If the parent's redirect exists, it will be used, overriding the default.
    if (v?.children && v.children.length && !v.redirect)
      v.redirect = v.children[0].path;
    // Parent's name value: If children exist and the parent's name doesn't, it defaults to the first child's name. If the parent's name exists, it will be used. (Note: In testing, a parent's name cannot be the same as a child's name, as it causes redirection to fail (404). Therefore, 'Parent' is automatically appended to the parent's name to avoid duplication.)
    if (v?.children && v.children.length && !v.name)
      v.name = (v.children[0].name as string) + "Parent";
    if (v.meta?.frameSrc) {
      v.component = IFrame;
    } else {
      // Compatibility for when the backend sends a component path versus when it doesn't. (If the backend sends a component path, the 'path' can be anything. If not, the component path will match the route 'path'.)
      const index = v?.component
        ? modulesRoutesKeys.findIndex(ev => ev.includes(v.component as any))
        : modulesRoutesKeys.findIndex(ev => ev.includes(v.path));
      v.component = modulesRoutes[modulesRoutesKeys[index]];
    }
    if (v?.children && v.children.length) {
      addAsyncRoutes(v.children);
    }
  });
  return arrRoutes;
}

/** Gets the router history mode. See: https://next.router.vuejs.org/guide/essentials/history-mode.html */
function getHistoryMode(routerHistory: any): RouterHistory {
  // len=1 means only history mode, len=2 means a base parameter exists in the history mode.
  const historyMode = routerHistory.split(",");
  const leftMode = historyMode[0];
  const rightMode = historyMode[1];
  // no param
  if (historyMode.length === 1) {
    if (leftMode === "hash") {
      return createWebHashHistory("");
    } else if (leftMode === "h5") {
      return createWebHistory("");
    }
  } // has param
  else if (historyMode.length === 2) {
    if (leftMode === "hash") {
      return createWebHashHistory(rightMode);
    } else if (leftMode === "h5") {
      return createWebHistory(rightMode);
    }
  }
}

/** Gets the current user's permissions. */
function getAuths(): Array<string> {
  // Get the complete list of user permissions from localStorage.
  // This is the most reliable source of truth.
  return storageLocal().getItem<DataInfo<string>>(userKey)?.permissions ?? [];
}

/** Checks if the user has button-level permissions. */
function hasAuth(value: string | Array<string>): boolean {
  if (!value) return false;
  // Get all permission codes for the current user.
  const metaAuths = getAuths();
  if (!metaAuths) return false;
  return isString(value)
    ? metaAuths.includes(value)
    : isIncludeAllChildren(value, metaAuths);
}

function handleTopMenu(route: any) {
  if (route?.children && route.children.length > 1) {
    if (route.redirect) {
      return route.children.filter(
        (cur: any) => cur.path === route.redirect
      )[0];
    } else {
      return route.children[0];
    }
  } else {
    return route;
  }
}

/** Gets the first menu item from all menus (the top-level menu). */
function getTopMenu(tag = false): menuType {
  const topMenu = handleTopMenu(
    usePermissionStoreHook().wholeMenus[0]?.children[0]
  );
  tag && useMultiTagsStoreHook().handleTags("push", topMenu);
  return topMenu;
}

export {
  hasAuth,
  getAuths,
  ascending,
  filterTree,
  initRouter,
  getTopMenu,
  addPathMatch,
  isOneOfArray,
  getHistoryMode,
  addAsyncRoutes,
  getParentPaths,
  findRouteByPath,
  handleAliveRoute,
  formatTwoStageRoutes,
  formatFlatteningRoutes,
  filterNoPermissionTree
};
