const Layout = () => import("@/layout/index.vue");

export default {
  path: "/message",
  name: "Message",
  component: Layout,
  redirect: "/message/management",
  meta: {
    icon: "ri/message-3-line",
    title: "Message Management",
    rank: 6
  },
  children: [
    {
      path: "/message/management",
      name: "MessageManagement",
      component: () => import("@/views/message/index.vue"),
      meta: {
        title: "Message Management",
        showLink: true,
        auths: ["message.read"]
      }
    }
  ]
} satisfies RouteConfigsTable;
