import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/model-ai/provider/utils/type";

export const getProviders = (params?: object) => {
  return http.request<Result>("get", "/api/auth/providers", {
    params
  });
};

export const getProviderById = (id: number) => {
  return http.request<Result>("get", `/api/auth/providers/${id}`);
};

export const createProvider = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/providers", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateProviderById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/providers/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const destroyProviderById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/providers/${id}`);
};

export const bulkDestroyProviders = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/providers/bulk-destroy", {
    data
  });
};

export const restoreProviderById = (id: number) => {
  return http.request<Result>("put", `/api/auth/providers/${id}/restore`);
};

export const bulkRestoreProviders = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/providers//bulk-restore", {
    data
  });
};

export const deleteProviderById = (id: number) => {
  return http.request<Result>(
    "delete",
    `/api/auth/providers/${id}/force-delete`
  );
};

export const bulkDeleteProviders = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/providers/bulk-delete", {
    data
  });
};

export const dropdownProviders = () => {
  return http.request<Result>("get", "/api/auth/providers/dropdown");
};
