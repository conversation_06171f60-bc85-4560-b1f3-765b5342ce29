<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { Plus, Message, Phone, Link } from "@element-plus/icons-vue";
import { spaceRules } from "@/views/space/utils/rule";

const props = defineProps<{
  visible: boolean;
  values: Record<string, any>;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: object): void;
  (e: "submit", values: object): void;
}>();

const showDrawer = computed({
  get() {
    return props.visible;
  },
  set(newValue) {
    emit("update:visible", newValue);
  }
});

const formRef = ref(null);

let companyForm = reactive({
  uuid: "",
  name: "",
  description: "",
  email: "",
  phone: "",
  logo: "",
  logoFile: null as File | null
});

const handleLogoChange = uploadFile => {
  companyForm.logo = URL.createObjectURL(uploadFile.raw);
  companyForm.logoFile = uploadFile.raw;
};

// Hàm xử lý khi submit form
const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      emit("submit", companyForm);
    } else {
      ElMessage({
        type: "error",
        message: "Vui lòng điền đầy đủ thông tin bắt buộc."
      });
    }
  });
};

const close = () => {
  showDrawer.value = false;
  formRef.value?.resetFields();
  companyForm.logo = "";
  companyForm.logoFile = null;
};

watch(
  () => props.visible,
  newValue => {
    if (newValue) {
      Object.assign(companyForm, props.values);
    } else {
      companyForm = reactive({
        uuid: "",
        name: "",
        description: "",
        email: "",
        phone: "",
        logo: "",
        logoFile: null as File | null
      });
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <el-dialog
    v-model="showDrawer"
    width="70%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-card class="rounded-2xl !shadow-lg">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-xl font-bold text-gray-800">Tạo công ty mới</span>
        </div>
      </template>
      <el-form
        ref="formRef"
        :model="companyForm"
        :rules="spaceRules"
        label-position="top"
        @submit.prevent="submitForm"
      >
        <div class="grid grid-cols-1 md:grid-cols-3 gap-x-8">
          <div class="md:col-span-1">
            <div class="w-full text-center mb-2">
              <h3 class="text-gray-500">Logo</h3>
            </div>
            <el-form-item prop="logo">
              <el-upload
                class="avatar-uploader mx-auto"
                action="#"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="handleLogoChange"
              >
                <img
                  v-if="companyForm.logo"
                  :src="companyForm.logo"
                  class="avatar"
                  alt="logo"
                />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="el-upload__tip text-center mx-auto">
                Nhấp để tải lên. Chỉ chấp nhận file JPG/PNG, dung lượng dưới
                2MB.
              </div>
            </el-form-item>
          </div>

          <!-- Cột phải: Thông tin chính -->
          <div class="md:col-span-2">
            <el-form-item label="Tên công ty" prop="name">
              <el-input
                v-model="companyForm.name"
                placeholder="Ví dụ: Công ty Cổ phần ABC"
                size="large"
              />
            </el-form-item>

            <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-4">
              <el-form-item label="Email" prop="email">
                <el-input
                  v-model="companyForm.email"
                  placeholder="<EMAIL>"
                  size="large"
                >
                  <template #prefix>
                    <el-icon>
                      <Message />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="Số điện thoại" prop="phone">
                <el-input
                  v-model="companyForm.phone"
                  placeholder="0123 456 789"
                  size="large"
                >
                  <template #prefix>
                    <el-icon>
                      <Phone />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </div>

            <el-form-item label="Mô tả" prop="description">
              <el-input
                v-model="companyForm.description"
                type="textarea"
                :rows="5"
                placeholder="Giới thiệu ngắn về công ty..."
              />
            </el-form-item>
          </div>
        </div>

        <el-divider />
        <div class="flex justify-end">
          <el-button round @click="close">
            <IconifyIconOnline
              icon="mdi:window-close"
              class="mr-2 text-red-600"
            />
            Đóng
          </el-button>
          <el-button round color="#626aef" @click="submitForm">
            <IconifyIconOnline icon="mdi:plus" class="mr-2" />
            {{ companyForm.uuid ? "Cập nhật" : "Tạo mới" }}
          </el-button>
        </div>
      </el-form>
    </el-card>
  </el-dialog>
</template>

<style lang="scss">
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader img {
  width: 178px;
  height: 178px;
  object-fit: cover;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
