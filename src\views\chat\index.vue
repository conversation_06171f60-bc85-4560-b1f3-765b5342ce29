<script setup lang="ts">
import { defineAsyncComponent, onBeforeMount, ref } from "vue";
import { useChatBotStoreHook } from "@/store/modules/chat";
import { useChatBot } from "@/views/chat/utils/hook";
import { useRoute, useRouter } from "vue-router";

const ChatSidebar = defineAsyncComponent(
  () => import("./components/ChatSidebar.vue")
);

const ChatMain = defineAsyncComponent(
  () => import("./components/ChatMain.vue")
);

const router = useRouter();
const route = useRoute();

console.log("useChatBotStoreHook:---------->", route.query);

const useChatBotStore = useChatBotStoreHook();

// Sử dụng hook ở đây thay vì trong từng component
const chatBot = useChatBot();

const mainView = ref("chat");

const getBotAgents = async () => {
  return await useChatBotStore.getChatBots();
};

// Event handlers
const handleSelectAgent = (agentUuid: string | number) => {
  console.log("Agent selected in index:", agentUuid);
  chatBot.selectAgent(agentUuid.toString());
};

const handleCreateNewAgent = () => {
  console.log("Create new agent");
  router.push("/bots/agent");
};

onBeforeMount(async () => {
  await getBotAgents();
  if (!useChatBotStore.selectedBot?.uuid && useChatBotStore.bots.length > 0) {
    useChatBotStore.getSelectedChatBot(useChatBotStore.bots[0].uuid);
  }
});
</script>

<template>
  <div class="main flex flex-col" style="height: calc(100% - 25px)">
    <div ref="contentRef" class="flex h-full">
      <ChatSidebar
        :chat-bot="chatBot"
        @select-agent="handleSelectAgent"
        @create-new-agent="handleCreateNewAgent"
      />
      <ChatMain :main-view="mainView" :chat-bot="chatBot" />
    </div>
  </div>
</template>

<style lang="scss">
.chat-messages::-webkit-scrollbar {
  width: 6px;
}
.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
}
.chat-messages::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}
.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.typing-indicator span {
  animation: bounce 1.4s infinite ease-in-out both;
}
@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
