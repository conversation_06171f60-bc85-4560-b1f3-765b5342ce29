import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/page/utils/type";

export const getPages = (params?: object) => {
  return http.request<Result>("get", "/api/auth/pages", {
    params
  });
};

export const getPageById = (id: number) => {
  return http.request<Result>("get", `/api/auth/pages/${id}`);
};

export const createPage = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/pages", {
    data: useConvertKeyToSnake(data)
  });
};

export const updatePageById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/pages/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const destroyPageById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/pages/${id}`);
};

export const bulkDestroyPages = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/pages/bulk-destroy", {
    data
  });
};

export const restorePageById = (id: number) => {
  return http.request<Result>("put", `/api/auth/pages/${id}/restore`);
};

export const bulkRestorePages = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/pages/bulk-restore", {
    data
  });
};

export const deletePageById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/pages/${id}/force-delete`);
};

export const bulkDeletePages = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/pages/bulk-delete", {
    data
  });
};

export const dropdownPages = () => {
  return http.request<Result>("get", "/api/auth/pages/dropdown");
};

// Translation support
export const getPageTranslations = (pageId: number) => {
  return http.request<Result>("get", `/api/auth/pages/${pageId}/translations`);
};

export const createPageTranslation = (pageId: number, data: any) => {
  return http.request<Result>(
    "post",
    `/api/auth/pages/${pageId}/translations`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const updatePageTranslation = (
  pageId: number,
  locale: string,
  data: any
) => {
  return http.request<Result>(
    "put",
    `/api/auth/pages/${pageId}/translations/${locale}`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const deletePageTranslation = (pageId: number, locale: string) => {
  return http.request<Result>(
    "delete",
    `/api/auth/pages/${pageId}/translations/${locale}`
  );
};
