import { $t } from "@/plugins/i18n";
import { hasAuth } from "@/router/utils";
import { ElAvatar, ElTag, ElTooltip } from "element-plus";
import { h } from "vue";
import dayjs from "dayjs";
import { StatusBadge } from "@/components/Shared";

export const columns = [
  {
    type: "selection",
    width: 55,
    align: "left",
    hide: () => !hasAuth("user.destroy")
  },
  {
    label: $t("ID"),
    prop: "id",
    minWidth: 80,
    sortable: "custom"
  },
  {
    label: $t("User"),
    prop: "name",
    minWidth: 200,
    cellRenderer: ({ row }) => {
      return h("div", { class: "flex items-center space-x-3" }, [
        h(ElAvatar, {
          size: 40,
          src: row.avatar,
          alt: row.name
        }, {
          default: () => row.name?.charAt(0).toUpperCase() || "U"
        }),
        h("div", { class: "flex flex-col" }, [
          h("span", { class: "font-medium text-gray-900" }, row.name),
          h("span", { class: "text-sm text-gray-500" }, row.email)
        ])
      ]);
    }
  },
  {
    label: $t("Status"),
    prop: "status",
    minWidth: 120,
    cellRenderer: ({ row }) => {
      return h(StatusBadge, {
        status: row.status,
        type: "user",
        size: "small"
      });
    }
  },
  {
    label: $t("Roles"),
    prop: "roles",
    minWidth: 150,
    cellRenderer: ({ row }) => {
      if (!row.roles || row.roles.length === 0) {
        return h("span", { class: "text-gray-400 text-sm" }, $t("No roles"));
      }
      
      const visibleRoles = row.roles.slice(0, 2);
      const remainingCount = row.roles.length - 2;
      
      return h("div", { class: "flex flex-wrap gap-1" }, [
        ...visibleRoles.map((role: any) => 
          h(ElTag, {
            size: "small",
            type: role.name === "super-admin" ? "danger" : "primary",
            effect: "light"
          }, () => role.display_name || role.name)
        ),
        remainingCount > 0 && h(ElTooltip, {
          content: row.roles.slice(2).map((r: any) => r.display_name || r.name).join(", "),
          placement: "top"
        }, {
          default: () => h(ElTag, {
            size: "small",
            type: "info",
            effect: "light"
          }, () => `+${remainingCount}`)
        })
      ]);
    }
  },
  {
    label: $t("Email Verified"),
    prop: "email_verified_at",
    minWidth: 120,
    cellRenderer: ({ row }) => {
      const isVerified = !!row.email_verified_at;
      return h("div", { class: "flex items-center space-x-2" }, [
        h("div", {
          class: [
            "w-2 h-2 rounded-full",
            isVerified ? "bg-green-500" : "bg-red-500"
          ]
        }),
        h("span", {
          class: [
            "text-sm",
            isVerified ? "text-green-600" : "text-red-600"
          ]
        }, isVerified ? $t("Verified") : $t("Unverified"))
      ]);
    }
  },
  {
    label: $t("Last Login"),
    prop: "last_login_at",
    minWidth: 150,
    cellRenderer: ({ row }) => {
      if (!row.last_login_at) {
        return h("span", { class: "text-gray-400 text-sm" }, $t("Never"));
      }
      
      const lastLogin = dayjs(row.last_login_at);
      const isRecent = lastLogin.isAfter(dayjs().subtract(1, "hour"));
      
      return h("div", { class: "flex flex-col" }, [
        h("span", {
          class: [
            "text-sm",
            isRecent ? "text-green-600 font-medium" : "text-gray-600"
          ]
        }, lastLogin.format("MMM DD, YYYY")),
        h("span", { class: "text-xs text-gray-400" }, lastLogin.format("HH:mm"))
      ]);
    }
  },
  {
    label: $t("Registration"),
    prop: "created_at",
    minWidth: 150,
    cellRenderer: ({ row }) => {
      const createdAt = dayjs(row.created_at);
      return h("div", { class: "flex flex-col" }, [
        h("span", { class: "text-sm text-gray-600" }, createdAt.format("MMM DD, YYYY")),
        h("span", { class: "text-xs text-gray-400" }, createdAt.format("HH:mm"))
      ]);
    }
  },
  {
    label: $t("Actions"),
    fixed: "right",
    width: 180,
    slot: "operation"
  }
];
