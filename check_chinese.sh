#!/bin/bash

echo "Checking for remaining Chinese characters in src directory..."

# Count files with Chinese characters (excluding backup files)
count=0
total_files=0

for file in $(find src -name "*.vue" -o -name "*.ts" -o -name "*.js" | grep -v "\.bak$"); do
    total_files=$((total_files + 1))
    if grep -q "[\u4e00-\u9fff]" "$file" 2>/dev/null; then
        count=$((count + 1))
        echo "Found Chinese in: $file"
        # Show a sample line with Chinese
        grep "[\u4e00-\u9fff]" "$file" | head -1
        echo "---"
    fi
done

echo "Summary:"
echo "Total files checked: $total_files"
echo "Files with Chinese characters: $count"
echo "Files successfully processed: $((total_files - count))"

if [ $count -eq 0 ]; then
    echo "🎉 SUCCESS: All Chinese characters have been replaced!"
else
    echo "⚠️  Still need to process $count files"
fi
