import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";
import { capitalized } from "@/utils/helpers";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    minWidth: 200,
    headerRenderer: () => $t("Knowledge Base Name")
  },
  {
    prop: "type",
    align: "center",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Type"),
    cellRenderer: ({ row }) => {
      const typeColors = {
        file: "primary",
        text: "success", 
        url: "warning",
        document: "info"
      };
      return h(
        ElTag,
        {
          type: typeColors[row.type] || "info",
          size: "small"
        },
        () => capitalized(row.type || "")
      );
    }
  },
  {
    prop: "ownerType",
    align: "center",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Owner Type"),
    cellRenderer: ({ row }) => capitalized(row.ownerType || "")
  },
  {
    prop: "ownerId",
    align: "center",
    sortable: true,
    width: 100,
    headerRenderer: () => $t("Owner ID")
  },
  {
    prop: "status",
    align: "center",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        active: "success",
        inactive: "info",
        processing: "warning",
        failed: "danger"
      };
      return h(
        ElTag,
        {
          type: statusColors[row.status] || "info",
          size: "small"
        },
        () => $t(capitalized(row.status)).toUpperCase()
      );
    }
  },
  {
    prop: "storagePath",
    align: "left",
    sortable: true,
    width: 180,
    headerRenderer: () => $t("Storage Path"),
    cellRenderer: ({ row }) => {
      if (!row.storagePath) return "-";
      const path = row.storagePath;
      return path.length > 30 ? `${path.substring(0, 30)}...` : path;
    }
  },
  {
    prop: "createdAt",
    width: 160,
    sortable: true,
    headerRenderer: () => $t("Created at"),
    formatter: ({ createdAt }) =>
      createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
