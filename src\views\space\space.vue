<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent, h } from "vue";
import { useSpaceHook } from "./utils/hook";
import { clone, deviceDetection } from "@pureadmin/utils";
import { useRoute, useRouter } from "vue-router";

// Lazy load components
const SpaceBotCard = defineAsyncComponent(
  () => import("./components/SpaceBotCard.vue")
);

const tableRef = ref();
const contentRef = ref();
const size = ref("default");

// Tab management
const activeTab = ref("ai-assistant");

const {
  // Data/State
  loading,
  filterRef,
  records,
  // Event handlers
  fnGetSpaces,
  fnHandleSortChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  spaceFormRef,
  handleSubmit,
  handleFilter,
  handleDestroy
} = useSpaceHook();

const router = useRouter();
const route = useRoute();

const handleEdit = (row: any) => {
  drawerValues.value = clone(row, true);
  drawerVisible.value = true;
};

const handleTabClick = (tab: any) => {
  console.log("Tab clicked:", tab.paneName);
};

const createAgent = () => {
  console.log("Create agent:-----------------> BOT");
  const currentSlug = route.params.slug || "ai-studio";
  router.push(`/spaces/${currentSlug}/agent`);
};

onMounted(() => {
  nextTick(() => {
    fnGetSpaces();
  });
});
</script>

<template>
  <div class="main">
    <div ref="contentRef">
      <header
        class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6"
      >
        <div>
          <h1 class="text-2xl font-bold text-gray-800">My Teams</h1>
          <p class="text-gray-500">Quản lý danh sách các công ty của bạn.</p>
        </div>
      </header>

      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl"
      >
        <div
          class="w-full h-full min-h-[356px] rounded-2xl border-2 border-dashed border-gray-300 flex flex-col items-center justify-center text-gray-500 hover:border-indigo-500 hover:text-indigo-500 transition-colors duration-300 cursor-pointer bg-white"
          @click="createAgent"
        >
          <IconifyIconOnline icon="line-md:plus" class="text-8xl" />
          <span class="font-semibold text-lg"> Tạo AI Agent </span>
        </div>
        <SpaceBotCard />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main {
  padding: 1rem;
}
</style>
