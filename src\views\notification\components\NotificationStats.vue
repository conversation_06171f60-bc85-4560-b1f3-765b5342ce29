<script setup lang="ts">
import { computed } from "vue";
import { $t } from "@/plugins/i18n";

interface Props {
  stats?: any;
}

const props = withDefaults(defineProps<Props>(), {
  stats: () => ({})
});

const statsCards = computed(() => [
  {
    title: $t("Total Notifications"),
    value: props.stats.total || 0,
    icon: "ri/notification-line",
    color: "text-blue-600",
    bgColor: "bg-blue-50"
  },
  {
    title: $t("Unread"),
    value: props.stats.unread || 0,
    icon: "ri/mail-unread-line",
    color: "text-orange-600",
    bgColor: "bg-orange-50"
  },
  {
    title: $t("Sent Today"),
    value: props.stats.sentToday || 0,
    icon: "ri/send-plane-line",
    color: "text-green-600",
    bgColor: "bg-green-50"
  },
  {
    title: $t("Scheduled"),
    value: props.stats.scheduled || 0,
    icon: "ri/time-line",
    color: "text-purple-600",
    bgColor: "bg-purple-50"
  }
]);

const typeStats = computed(() => {
  if (!props.stats.byType) return [];
  return Object.entries(props.stats.byType).map(([type, count]) => ({
    type,
    count,
    label: getTypeLabel(type)
  }));
});

const priorityStats = computed(() => {
  if (!props.stats.byPriority) return [];
  return Object.entries(props.stats.byPriority).map(([priority, count]) => ({
    priority,
    count,
    label: getPriorityLabel(priority)
  }));
});

function getTypeLabel(type: string): string {
  const typeMap = {
    info: $t("Info"),
    success: $t("Success"),
    warning: $t("Warning"),
    error: $t("Error"),
    announcement: $t("Announcement")
  };
  return typeMap[type] || type;
}

function getPriorityLabel(priority: string): string {
  const priorityMap = {
    low: $t("Low"),
    medium: $t("Medium"),
    high: $t("High"),
    urgent: $t("Urgent")
  };
  return priorityMap[priority] || priority;
}
</script>

<template>
  <div class="notification-stats">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div
        v-for="card in statsCards"
        :key="card.title"
        class="bg-white rounded-lg shadow p-6"
      >
        <div class="flex items-center">
          <div :class="[card.bgColor, 'p-3 rounded-lg']">
            <iconify-icon-online
              :icon="card.icon"
              :class="[card.color, 'text-xl']"
            />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ card.title }}</p>
            <p class="text-2xl font-bold text-gray-900">{{ card.value }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Type Distribution -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
          {{ $t("By Type") }}
        </h3>
        <div class="space-y-3">
          <div
            v-for="item in typeStats"
            :key="item.type"
            class="flex items-center justify-between"
          >
            <div class="flex items-center">
              <div
                :class="[
                  'w-3 h-3 rounded-full mr-3',
                  getTypeColor(item.type)
                ]"
              ></div>
              <span class="text-sm text-gray-600">{{ item.label }}</span>
            </div>
            <span class="text-sm font-medium text-gray-900">{{ item.count }}</span>
          </div>
        </div>
      </div>

      <!-- Priority Distribution -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
          {{ $t("By Priority") }}
        </h3>
        <div class="space-y-3">
          <div
            v-for="item in priorityStats"
            :key="item.priority"
            class="flex items-center justify-between"
          >
            <div class="flex items-center">
              <div
                :class="[
                  'w-3 h-3 rounded-full mr-3',
                  getPriorityColor(item.priority)
                ]"
              ></div>
              <span class="text-sm text-gray-600">{{ item.label }}</span>
            </div>
            <span class="text-sm font-medium text-gray-900">{{ item.count }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
function getTypeColor(type: string): string {
  const colorMap = {
    info: "bg-blue-500",
    success: "bg-green-500",
    warning: "bg-yellow-500",
    error: "bg-red-500",
    announcement: "bg-purple-500"
  };
  return colorMap[type] || "bg-gray-500";
}

function getPriorityColor(priority: string): string {
  const colorMap = {
    low: "bg-gray-400",
    medium: "bg-blue-500",
    high: "bg-orange-500",
    urgent: "bg-red-500"
  };
  return colorMap[priority] || "bg-gray-500";
}
</script>

<style scoped>
.notification-stats {
  @apply p-4;
}
</style>
