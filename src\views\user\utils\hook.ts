import { reactive, ref } from "vue";
import {
  getUsers,
  createUser,
  updateUserById,
  deleteUserById,
  bulkDeleteUsers,
  bulkDestroyUsers,
  destroyUserById,
  restoreUserById,
  bulkRestoreUsers,
  updateUserStatus,
  updateUserPassword,
  updateUserRoles,
  updateUserPermissions,
  suspendUser,
  unsuspendUser,
  banUser,
  unbanUser,
  sendPasswordReset,
  sendEmailVerification,
  verifyUserEmail,
  getRoles,
  getPermissions
} from "../utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { UserFilterProps } from "@/views/user/utils/type";

export function useUserHook() {
  // Reactive state
  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 50, 100]
  });

  const records = ref([]);
  const loading = ref(false);
  const multipleSelection = ref([]);

  // Form states
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref({});
  const userFormRef = ref();
  const filterRef = ref();

  // Additional states
  const rolesData = ref([]);
  const permissionsData = ref([]);
  const passwordDialogVisible = ref(false);
  const rolesDialogVisible = ref(false);
  const permissionsDialogVisible = ref(false);
  const selectedUserId = ref(null);

  // Fetch users
  const fnGetUsers = async (params?: FieldValues) => {
    loading.value = true;
    try {
      const { data } = await getUsers({
        page: pagination.currentPage,
        per_page: pagination.pageSize,
        ...params
      });

      if (data?.success) {
        const result = useConvertKeyToCamel(data.data);
        records.value = result.data || [];
        pagination.total = result.total || 0;
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      message($t("Failed to fetch users"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  // Pagination handlers
  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetUsers();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetUsers();
  };

  const fnHandleSelectionChange = (selection: any[]) => {
    multipleSelection.value = selection;
  };

  const fnHandleSortChange = ({ prop, order }: any) => {
    const sortBy = prop;
    const sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetUsers({ sort_by: sortBy, sort_order: sortOrder });
  };

  // CRUD operations
  const handleSubmit = async (values: FieldValues) => {
    try {
      const isEdit = !!drawerValues.value?.id;
      const apiCall = isEdit 
        ? updateUserById(drawerValues.value.id, values)
        : createUser(values);

      const { data } = await apiCall;
      
      if (data?.success) {
        message($t(isEdit ? "Updated successfully" : "Created successfully"), { 
          type: "success" 
        });
        drawerVisible.value = false;
        fnGetUsers();
      }
    } catch (error) {
      console.error("Error submitting user:", error);
      message($t("Operation failed"), { type: "error" });
    }
  };

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this user?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      const { data } = await deleteUserById(row.id);
      if (data?.success) {
        message($t("Deleted successfully"), { type: "success" });
        fnGetUsers();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting user:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected users?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      const ids = multipleSelection.value.map((item: any) => item.id);
      const { data } = await bulkDeleteUsers(ids);
      
      if (data?.success) {
        message($t("Deleted successfully"), { type: "success" });
        multipleSelection.value = [];
        fnGetUsers();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk deleting users:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  // Permanent delete operations
  const handleDestroy = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this user? This action cannot be undone."),
        $t("Danger"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );

      const { data } = await destroyUserById(row.id);
      if (data?.success) {
        message($t("Permanently deleted"), { type: "success" });
        fnGetUsers();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error destroying user:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkDestroy = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete selected users? This action cannot be undone."),
        $t("Danger"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );

      const ids = multipleSelection.value.map((item: any) => item.id);
      const { data } = await bulkDestroyUsers(ids);
      
      if (data?.success) {
        message($t("Permanently deleted"), { type: "success" });
        multipleSelection.value = [];
        fnGetUsers();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk destroying users:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  // Restore operations
  const handleRestore = async (row: any) => {
    try {
      const { data } = await restoreUserById(row.id);
      if (data?.success) {
        message($t("Restored successfully"), { type: "success" });
        fnGetUsers();
      }
    } catch (error) {
      console.error("Error restoring user:", error);
      message($t("Restore failed"), { type: "error" });
    }
  };

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      const ids = multipleSelection.value.map((item: any) => item.id);
      const { data } = await bulkRestoreUsers(ids);
      
      if (data?.success) {
        message($t("Restored successfully"), { type: "success" });
        multipleSelection.value = [];
        fnGetUsers();
      }
    } catch (error) {
      console.error("Error bulk restoring users:", error);
      message($t("Restore failed"), { type: "error" });
    }
  };

  // Status operations
  const handleStatusChange = async (row: any, status: string) => {
    try {
      const { data } = await updateUserStatus(row.id, status);
      if (data?.success) {
        message($t("Status updated successfully"), { type: "success" });
        fnGetUsers();
      }
    } catch (error) {
      console.error("Error updating status:", error);
      message($t("Status update failed"), { type: "error" });
    }
  };

  const handleSuspend = async (row: any) => {
    try {
      const { value: reason } = await ElMessageBox.prompt(
        $t("Please enter suspension reason"),
        $t("Suspend User"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          inputPlaceholder: $t("Suspension reason")
        }
      );

      const { data } = await suspendUser(row.id, reason);
      if (data?.success) {
        message($t("User suspended successfully"), { type: "success" });
        fnGetUsers();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error suspending user:", error);
        message($t("Suspend failed"), { type: "error" });
      }
    }
  };

  const handleUnsuspend = async (row: any) => {
    try {
      const { data } = await unsuspendUser(row.id);
      if (data?.success) {
        message($t("User unsuspended successfully"), { type: "success" });
        fnGetUsers();
      }
    } catch (error) {
      console.error("Error unsuspending user:", error);
      message($t("Unsuspend failed"), { type: "error" });
    }
  };

  const handleBan = async (row: any) => {
    try {
      const { value: reason } = await ElMessageBox.prompt(
        $t("Please enter ban reason"),
        $t("Ban User"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          inputPlaceholder: $t("Ban reason")
        }
      );

      const { data } = await banUser(row.id, reason);
      if (data?.success) {
        message($t("User banned successfully"), { type: "success" });
        fnGetUsers();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error banning user:", error);
        message($t("Ban failed"), { type: "error" });
      }
    }
  };

  const handleUnban = async (row: any) => {
    try {
      const { data } = await unbanUser(row.id);
      if (data?.success) {
        message($t("User unbanned successfully"), { type: "success" });
        fnGetUsers();
      }
    } catch (error) {
      console.error("Error unbanning user:", error);
      message($t("Unban failed"), { type: "error" });
    }
  };

  // Email operations
  const handleSendPasswordReset = async (row: any) => {
    try {
      const { data } = await sendPasswordReset(row.id);
      if (data?.success) {
        message($t("Password reset email sent"), { type: "success" });
      }
    } catch (error) {
      console.error("Error sending password reset:", error);
      message($t("Failed to send password reset"), { type: "error" });
    }
  };

  const handleSendEmailVerification = async (row: any) => {
    try {
      const { data } = await sendEmailVerification(row.id);
      if (data?.success) {
        message($t("Verification email sent"), { type: "success" });
      }
    } catch (error) {
      console.error("Error sending email verification:", error);
      message($t("Failed to send verification email"), { type: "error" });
    }
  };

  const handleVerifyEmail = async (row: any) => {
    try {
      const { data } = await verifyUserEmail(row.id);
      if (data?.success) {
        message($t("Email verified successfully"), { type: "success" });
        fnGetUsers();
      }
    } catch (error) {
      console.error("Error verifying email:", error);
      message($t("Email verification failed"), { type: "error" });
    }
  };

  // Load roles and permissions
  const loadRoles = async () => {
    try {
      const { data } = await getRoles();
      if (data?.success) {
        rolesData.value = data.data || [];
      }
    } catch (error) {
      console.error("Error loading roles:", error);
    }
  };

  const loadPermissions = async () => {
    try {
      const { data } = await getPermissions();
      if (data?.success) {
        permissionsData.value = data.data || [];
      }
    } catch (error) {
      console.error("Error loading permissions:", error);
    }
  };

  // Filter handler
  const handleFilter = async (values: FieldValues) => {
    filterVisible.value = false;
    pagination.currentPage = 1;
    await fnGetUsers(values);
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    rolesData,
    permissionsData,
    
    // Event handlers
    handleBulkDelete,
    handleDelete,
    fnGetUsers,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    
    // Form related
    filterVisible,
    drawerVisible,
    drawerValues,
    userFormRef,
    handleSubmit,
    handleFilter,
    
    // Advanced operations
    handleBulkDestroy,
    handleBulkRestore,
    handleDestroy,
    handleRestore,
    handleStatusChange,
    handleSuspend,
    handleUnsuspend,
    handleBan,
    handleUnban,
    handleSendPasswordReset,
    handleSendEmailVerification,
    handleVerifyEmail,
    
    // Dialog states
    passwordDialogVisible,
    rolesDialogVisible,
    permissionsDialogVisible,
    selectedUserId,
    
    // Data loaders
    loadRoles,
    loadPermissions
  };
}
