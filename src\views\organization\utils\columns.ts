import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";
import { capitalized } from "@/utils/helpers";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    minWidth: 180,
    headerRenderer: () => $t("Organization Name")
  },
  {
    prop: "email",
    align: "left",
    sortable: true,
    minWidth: 160,
    headerRenderer: () => $t("Email")
  },
  {
    prop: "organizationType",
    align: "center",
    sortable: true,
    width: 140,
    headerRenderer: () => $t("Type"),
    cellRenderer: ({ row }) => capitalized(row.organizationType || "")
  },
  {
    prop: "industry",
    align: "left",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Industry")
  },
  {
    prop: "country",
    align: "center",
    sortable: true,
    width: 100,
    headerRenderer: () => $t("Country")
  },
  {
    prop: "status",
    align: "center",
    sortable: true,
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.status === "active" ? "success" : row.status === "inactive" ? "warning" : "danger",
          size: "small"
        },
        () => $t(capitalized(row.status)).toUpperCase()
      );
    }
  },
  {
    prop: "isVerified",
    align: "center",
    sortable: true,
    width: 100,
    headerRenderer: () => $t("Verified"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.isVerified ? "success" : "info",
          size: "small"
        },
        () => row.isVerified ? $t("Yes") : $t("No")
      );
    }
  },
  {
    prop: "employeeCount",
    align: "center",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Employees"),
    cellRenderer: ({ row }) => row.employeeCount || "-"
  },
  {
    prop: "createdAt",
    width: 160,
    sortable: true,
    headerRenderer: () => $t("Created at"),
    formatter: ({ createdAt }) =>
      createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
