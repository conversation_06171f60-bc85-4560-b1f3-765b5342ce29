#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

def check_chinese_in_store():
    """Check for Chinese characters in store directory"""
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
    
    # Find all TypeScript files in store directory
    store_files = glob.glob("src/store/**/*.ts", recursive=True)
    
    files_with_chinese = []
    total_files = len(store_files)
    
    print(f"🔍 Checking {total_files} files in src/store directory...")
    print("=" * 60)
    
    for filepath in store_files:
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                matches = chinese_pattern.findall(content)
                if matches:
                    files_with_chinese.append(filepath)
                    print(f"❌ Chinese found in: {filepath}")
                    # Show lines with Chinese
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if chinese_pattern.search(line):
                            print(f"   Line {i}: {line.strip()}")
                    print("---")
                else:
                    print(f"✅ Clean: {filepath}")
        except Exception as e:
            print(f"❗ Error reading {filepath}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 SUMMARY:")
    print(f"Total files checked: {total_files}")
    print(f"Files with Chinese: {len(files_with_chinese)}")
    print(f"Files clean: {total_files - len(files_with_chinese)}")
    
    if len(files_with_chinese) == 0:
        print("🎉 SUCCESS: All store files are free of Chinese characters!")
    else:
        print(f"⚠️  {len(files_with_chinese)} files still contain Chinese characters")
        
    return files_with_chinese

if __name__ == "__main__":
    check_chinese_in_store()
