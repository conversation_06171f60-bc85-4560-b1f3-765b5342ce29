import { $t } from "@/plugins/i18n";

export function columns() {
  return [
    {
      label: $t("Selection"),
      type: "selection",
      width: 55,
      align: "left",
      headerAlign: "left"
    },
    {
      label: $t("ID"),
      prop: "id",
      minWidth: 80,
      align: "center",
      headerAlign: "center"
    },
    {
      label: $t("Translation Key"),
      prop: "key",
      minWidth: 200,
      align: "left",
      headerAlign: "left",
      cellRenderer: ({ row }) => (
        <span class="font-mono text-sm">{row.key}</span>
      )
    },
    {
      label: $t("Language"),
      prop: "languageCode",
      minWidth: 100,
      align: "center",
      headerAlign: "center",
      cellRenderer: ({ row }) => (
        <el-tag size="small" type="primary" effect="plain">
          {row.languageCode?.toUpperCase()}
        </el-tag>
      )
    },
    {
      label: $t("Translation Value"),
      prop: "value",
      minWidth: 250,
      align: "left",
      headerAlign: "left",
      cellRenderer: ({ row }) => (
        <div class="max-w-xs">
          <el-tooltip
            content={row.value}
            placement="top"
            effect="dark"
            disabled={!row.value || row.value.length <= 50}
          >
            <span class="truncate block">
              {row.value && row.value.length > 50 
                ? `${row.value.substring(0, 50)}...` 
                : row.value}
            </span>
          </el-tooltip>
        </div>
      )
    },
    {
      label: $t("Group"),
      prop: "group",
      minWidth: 120,
      align: "center",
      headerAlign: "center",
      cellRenderer: ({ row }) => (
        row.group ? (
          <el-tag size="small" type="info" effect="plain">
            {row.group}
          </el-tag>
        ) : (
          <span class="text-gray-400">-</span>
        )
      )
    },
    {
      label: $t("Plural"),
      prop: "isPlural",
      minWidth: 80,
      align: "center",
      headerAlign: "center",
      cellRenderer: ({ row, props }) => (
        <el-tag
          size={props.size}
          type={row.isPlural ? "warning" : "info"}
          effect="plain"
        >
          {row.isPlural ? $t("Yes") : $t("No")}
        </el-tag>
      )
    },
    {
      label: $t("Status"),
      prop: "status",
      minWidth: 100,
      align: "center",
      headerAlign: "center",
      cellRenderer: ({ row, props }) => (
        <el-tag
          size={props.size}
          type={row.status === "active" ? "success" : "danger"}
          effect="plain"
        >
          {row.status === "active" ? $t("Active") : $t("Inactive")}
        </el-tag>
      )
    },
    {
      label: $t("Created At"),
      prop: "createdAt",
      minWidth: 180,
      align: "center",
      headerAlign: "center",
      formatter: ({ createdAt }) =>
        createdAt ? new Date(createdAt).toLocaleString() : ""
    },
    {
      label: $t("Operations"),
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];
}
