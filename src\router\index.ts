// import "@/utils/sso";
import Cookies from "js-cookie";
import { getConfig } from "@/config";
import NProgress from "@/utils/progress";
import { buildHierarchyTree } from "@/utils/tree";
import remainingRouter from "./modules/remaining";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { usePermissionStoreHook } from "@/store/modules/permission";
import {
  isUrl,
  openLink,
  cloneDeep,
  isAllEmpty,
  storageLocal
} from "@pureadmin/utils";
import {
  ascending,
  getTopMenu,
  initRouter,
  isOneOfArray,
  getHistoryMode,
  findRouteByPath,
  handleAliveRoute,
  formatTwoStageRoutes,
  formatFlatteningRoutes
} from "./utils";
import {
  type Router,
  type RouteRecordRaw,
  type RouteComponent,
  createRouter
} from "vue-router";
import {
  type DataInfo,
  userKey,
  removeToken,
  multipleTabs<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from "@/utils/auth";

/** Automatically import all static routes, no need to manually import! Matches all files with .ts extension in src/router/modules directory (any nesting level), except remaining.ts file
 * How to match all files: https://github.com/mrmlnc/fast-glob#basic-syntax
 * How to exclude files: https://vitejs.dev/guide/features.html#negative-patterns
 */
const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
  {
    eager: true
  }
);

/** Original static routes (without any processing) */
const routes = [];

Object.keys(modules).forEach(key => {
  routes.push(modules[key].default);
});

/** Export processed static routes (all routes of level 3 and above are flattened to level 2) */
export const constantRoutes: Array<RouteRecordRaw> = formatTwoStageRoutes(
  formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
);

/** Initial static routes, used to reset routes when logging out */
const initConstantRoutes: Array<RouteRecordRaw> = cloneDeep(constantRoutes);

/** Used for rendering menus, maintaining original hierarchy */
export const constantMenus: Array<RouteComponent> = ascending(
  routes.flat(Infinity)
).concat(...remainingRouter);

/** Routes that do not participate in menu */
export const remainingPaths = Object.keys(remainingRouter).map(v => {
  return remainingRouter[v].path;
});

/** Create router instance */
export const router: Router = createRouter({
  history: getHistoryMode(import.meta.env.VITE_ROUTER_HISTORY),
  routes: constantRoutes.concat(...(remainingRouter as any)),
  strict: true,
  scrollBehavior(to, from, savedPosition) {
    return new Promise(resolve => {
      if (savedPosition) {
        return savedPosition;
      } else {
        if (from.meta.saveSrollTop) {
          const top: number =
            document.documentElement.scrollTop || document.body.scrollTop;
          resolve({ left: 0, top });
        }
      }
    });
  }
});

/** Reset router */
export function resetRouter() {
  router.clearRoutes();
  for (const route of initConstantRoutes.concat(...(remainingRouter as any))) {
    router.addRoute(route);
  }
  router.options.routes = formatTwoStageRoutes(
    formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
  );
  usePermissionStoreHook().clearAllCachePage();
}

/** Route whitelist */
const whiteList = [
  "/login",
  "/register",
  "/forget",
  "/reset-password",
  "/verify-email"
];

const { VITE_HIDE_HOME } = import.meta.env;

router.beforeEach((to: ToRouteType, _from, next) => {
  // Debug logging for space routes
  if (to.path.includes('/space') || to.path.includes('/spaces')) {
    console.log("Router navigation:", {
      to: to.path,
      from: _from.path,
      toParams: to.params,
      fromParams: _from.params,
      toName: to.name,
      fromName: _from.name
    });

    // Prevent navigation to /space/NaN or similar invalid routes
    if (to.path.includes('/space/NaN') || to.path.includes('/spaces/NaN') ||
        (to.params.slug && (to.params.slug === 'NaN' || to.params.slug === 'undefined'))) {
      console.warn("Preventing navigation to invalid space route:", to.path);
      next({ path: "/spaces" }); // Redirect to spaces list
      return;
    }
  }

  if (to.meta?.keepAlive) {
    handleAliveRoute(to, "add");
    // Page overall refresh and tab click refresh
    if (_from.name === undefined || _from.name === "Redirect") {
      handleAliveRoute(to);
    }
  }
  const userInfo = storageLocal().getItem<DataInfo<number>>(userKey);
  NProgress.start();
  const externalLink = isUrl(to?.name as string);
  if (!externalLink) {
    to.matched.some(item => {
      if (!item.meta?.title) return "";
      const Title = getConfig().Title;
      if (Title) document.title = `${item.meta.title} | ${Title}`;
      else document.title = item.meta.title as string;
    });
  }
  /** If already logged in and login information exists, cannot jump to route whitelist, but continue to stay on current page */
  function toCorrectRoute() {
    whiteList.includes(to.fullPath) ? next(_from.fullPath) : next();
  }
  if (Cookies.get(TokenKey) && userInfo) {
    // No permission, jump to 403 page
    if (to.meta?.roles && !isOneOfArray(to.meta?.roles, userInfo?.roles)) {
      next({ path: "/error/403" });
    }
    // After enabling hide homepage, manually entering homepage welcome route in browser address bar will jump to 404 page
    if (VITE_HIDE_HOME === "true" && to.fullPath === "/welcome") {
      next({ path: "/error/404" });
    }
    if (_from?.name) {
      // name is hyperlink
      if (externalLink) {
        openLink(to?.name as string);
        NProgress.done();
      } else {
        toCorrectRoute();
      }
    } else {
      // Refresh
      if (
        usePermissionStoreHook().wholeMenus.length === 0 &&
        to.path !== "/login"
      ) {
        initRouter()
          .then((router: Router) => {
            if (!useMultiTagsStoreHook().getMultiTagsCache) {
              const { path } = to;
              const route = findRouteByPath(
                path,
                router.options.routes[0].children
              );
              getTopMenu(true);
              // Tabs with query and params mode route parameters are not handled here
              if (route && route.meta?.title) {
                if (isAllEmpty(route.parentId) && route.meta?.backstage) {
                  // This is a dynamic top-level route (directory)
                  const { path, name, meta } = route.children[0];
                  useMultiTagsStoreHook().handleTags("push", {
                    path,
                    name,
                    meta
                  });
                } else {
                  const { path, name, meta } = route;
                  useMultiTagsStoreHook().handleTags("push", {
                    path,
                    name,
                    meta
                  });
                }
              }
            }
            // Ensure dynamic routes are fully added to the route list without affecting static routes (Note: router.beforeEach may trigger twice when dynamic routes refresh, the first trigger dynamic routes are not fully added, the second time dynamic routes are fully added to the route list, if you need to make some judgments in router.beforeEach, you can judge under the condition that to.name exists, so it will only trigger once)
            if (isAllEmpty(to.name)) router.push(to.fullPath);
          })
          .catch(error => {
            console.error("Router initialization failed:", error);
            // If router init fails, redirect to login
            removeToken();
            next({ path: "/login" });
          });
      }
      toCorrectRoute();
    }
  } else {
    if (to.path !== "/login") {
      if (whiteList.indexOf(to.path) !== -1) {
        next();
      } else {
        removeToken();
        next({ path: "/login" });
      }
    } else {
      next();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
