import type { VNode } from "vue";
import { isFunction } from "@pureadmin/utils";
import { type MessageHandler, ElMessage } from "element-plus";

type messageStyle = "el" | "antd";
type messageTypes = "info" | "success" | "warning" | "error";

interface MessageParams {
  /** MessageType，Optional `info` 、`success` 、`warning` 、`error` ，默认 `info` */
  type?: messageTypes;
  /** 是否纯色，默认 `false` */
  plain?: boolean;
  /** 自定义Icon，该Property会覆盖 `type` 的Icon */
  icon?: any;
  /** 是否将 `message` Property作为 `HTML` 片段处理，默认 `false` */
  dangerouslyUseHTMLString?: boolean;
  /** Message风格，Optional `el` 、`antd` ，默认 `antd` */
  customClass?: messageStyle;
  /** 显示Time，单位为毫秒。设为 `0` 则不会自动Close，`element-plus` 默认是 `3000` ，平台改成默认 `2000` */
  duration?: number;
  /** 是否显示CloseButton，默认值 `false` */
  showClose?: boolean;
  /** `Message` 距离窗口Top的偏移量，默认 `16` */
  offset?: number;
  /** SettingsComponent的根元素，默认 `document.body` */
  appendTo?: string | HTMLElement;
  /** 合并Content相同的Message，不支持 `VNode` Type的Message，默认值 `false` */
  grouping?: boolean;
  /** 重复次数，类似于 `Badge` 。当和 `grouping` Property一起使用时作为初始Quantity使用，默认值 `1` */
  repeatNum?: number;
  /** Close时的CallbackFunction, Parameter为被Close的 `message` 实例 */
  onClose?: Function | null;
}

/** 用法非常简单，参考 src/views/components/message/index.vue File */

/**
 * `Message` MessageTipFunction
 */
const message = (
  message: string | VNode | (() => VNode),
  params?: MessageParams
): MessageHandler => {
  if (!params) {
    return ElMessage({
      message,
      customClass: "pure-message"
    });
  } else {
    const {
      icon,
      type = "info",
      plain = false,
      dangerouslyUseHTMLString = false,
      customClass = "antd",
      duration = 2000,
      showClose = false,
      offset = 16,
      appendTo = document.body,
      grouping = false,
      repeatNum = 1,
      onClose
    } = params;

    return ElMessage({
      message,
      icon,
      type,
      plain,
      dangerouslyUseHTMLString,
      duration,
      showClose,
      offset,
      appendTo,
      grouping,
      repeatNum,
      // 全局搜 pure-message 即可知道该类的Style位置
      customClass: customClass === "antd" ? "pure-message" : "",
      onClose: () => (isFunction(onClose) ? onClose() : null)
    });
  }
};

/**
 * Close所有 `Message` MessageTipFunction
 */
const closeAllMessage = (): void => ElMessage.closeAll();

export { message, closeAllMessage };
