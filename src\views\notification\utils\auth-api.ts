import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/notification/utils/type";

export const getNotifications = (params?: object) => {
  return http.request<Result>("get", "/api/v1/auth/notifications", {
    params
  });
};

export const getNotificationById = (id: number) => {
  return http.request<Result>("get", `/api/v1/auth/notifications/${id}`);
};

export const createNotification = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/v1/auth/notifications", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateNotificationById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/v1/auth/notifications/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const deleteNotificationById = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/notifications/${id}/force-delete`);
};

export const bulkDeleteNotifications = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/notifications/bulk-force-delete", {
    data
  });
};

export const destroyNotificationById = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/notifications/${id}`);
};

export const bulkDestroyNotifications = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/notifications/bulk-destroy", {
    data
  });
};

export const restoreNotificationById = (id: number) => {
  return http.request<Result>("put", `/api/v1/auth/notifications/${id}/restore`);
};

export const bulkRestoreNotifications = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/v1/auth/notifications/bulk-restore", {
    data
  });
};

// Notification specific actions
export const sendNotification = (id: number) => {
  return http.request<Result>("post", `/api/v1/auth/notifications/${id}/send`);
};

export const markAsRead = (id: number) => {
  return http.request<Result>("put", `/api/v1/auth/notifications/${id}/read`);
};

export const markAsUnread = (id: number) => {
  return http.request<Result>("put", `/api/v1/auth/notifications/${id}/unread`);
};

export const bulkMarkAsRead = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/v1/auth/notifications/bulk-read", {
    data
  });
};

export const bulkMarkAsUnread = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/v1/auth/notifications/bulk-unread", {
    data
  });
};

export const getNotificationStats = () => {
  return http.request<Result>("get", "/api/v1/auth/notifications/stats");
};

export const getNotificationRecipients = (id: number, params?: object) => {
  return http.request<Result>("get", `/api/v1/auth/notifications/${id}/recipients`, {
    params
  });
};

export const getNotificationTemplates = (params?: object) => {
  return http.request<Result>("get", "/api/v1/auth/notification-templates", {
    params
  });
};

export const createNotificationTemplate = (data: any) => {
  return http.request<Result>("post", "/api/v1/auth/notification-templates", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateNotificationTemplate = (id: number, data: any) => {
  return http.request<Result>("put", `/api/v1/auth/notification-templates/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const deleteNotificationTemplate = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/notification-templates/${id}`);
};

// User notifications (for current user)
export const getUserNotifications = (params?: object) => {
  return http.request<Result>("get", "/api/v1/auth/user/notifications", {
    params
  });
};

export const markUserNotificationAsRead = (id: number) => {
  return http.request<Result>("put", `/api/v1/auth/user/notifications/${id}/read`);
};

export const markAllUserNotificationsAsRead = () => {
  return http.request<Result>("put", "/api/v1/auth/user/notifications/read-all");
};

export const getUserNotificationStats = () => {
  return http.request<Result>("get", "/api/v1/auth/user/notifications/stats");
};
