<script setup lang="ts">
import { useRouter } from "vue-router";
import { $t } from "@/plugins/i18n";

interface Props {
  showLogin?: boolean;
  showRegister?: boolean;
  showForgotPassword?: boolean;
  showBackToLogin?: boolean;
  customLinks?: Array<{
    text: string;
    path: string;
    query?: Record<string, any>;
  }>;
}

const props = withDefaults(defineProps<Props>(), {
  showLogin: true,
  showRegister: true,
  showForgotPassword: false,
  showBackToLogin: false,
  customLinks: () => []
});

const router = useRouter();

const goToLogin = () => {
  router.push("/login");
};

const goToRegister = () => {
  router.push("/register");
};

const goToForgotPassword = () => {
  router.push("/forget");
};

const goToCustomLink = (link: any) => {
  router.push({
    path: link.path,
    query: link.query
  });
};
</script>

<template>
  <div class="auth-navigation">
    <div class="flex justify-between items-center">
      <!-- Left side links -->
      <div class="flex gap-2">
        <el-button
          v-if="showBackToLogin"
          link
          type="primary"
          @click="goToLogin"
        >
          {{ $t("Back to Login") }}
        </el-button>

        <el-button
          v-if="showForgotPassword"
          link
          type="primary"
          @click="goToForgotPassword"
        >
          {{ $t("Forgot Password?") }}
        </el-button>
      </div>

      <!-- Right side links -->
      <div class="flex gap-2">
        <el-button v-if="showLogin" link type="primary" @click="goToLogin">
          {{ $t("Sign In") }}
        </el-button>

        <el-button
          v-if="showRegister"
          link
          type="primary"
          @click="goToRegister"
        >
          {{ $t("Sign Up") }}
        </el-button>

        <!-- Custom links -->
        <el-button
          v-for="link in customLinks"
          :key="link.path"
          link
          type="primary"
          @click="goToCustomLink(link)"
        >
          {{ link.text }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.auth-navigation {
  margin-top: 1rem;
}
</style>
