import type { CSSProperties, VNode, Component } from "vue";

type DoneFn = (cancel?: boolean) => void;
type EventType =
  | "open"
  | "close"
  | "openAutoFocus"
  | "closeAutoFocus"
  | "fullscreenCallBack";
type ArgsType = {
  /** `cancel` click cancel button, `sure` click confirm button, `close` click top-right close button or blank area or press esc key */
  command: "cancel" | "sure" | "close";
};
type ButtonType =
  | "primary"
  | "success"
  | "warning"
  | "danger"
  | "info"
  | "text";

/** https://element-plus.org/en-US/component/dialog.html#attributes */
type DialogProps = {
  /** Show/hide state of `Dialog` */
  visible?: boolean;
  /** Title of `Dialog` */
  title?: string;
  /** Width of `Dialog`, default `50%` */
  width?: string | number;
  /** Whether it's a fullscreen `Dialog` (will always be in fullscreen state unless dialog is closed), default `false`, when both `fullscreen` and `fullscreenIcon` are passed, only `fullscreen` takes effect */
  fullscreen?: boolean;
  /** Whether to show fullscreen operation icon, default `false`, when both `fullscreen` and `fullscreenIcon` are passed, only `fullscreen` takes effect */
  fullscreenIcon?: boolean;
  /** `margin-top` value in `Dialog CSS`, default `15vh` */
  top?: string;
  /** Whether a mask layer is needed, default `true` */
  modal?: boolean;
  /** Whether `Dialog` itself is inserted into the `body` element. Nested `Dialog` must specify this attribute and set it to `true`, default `false` */
  appendToBody?: boolean;
  /** Whether to lock `body` scrolling when `Dialog` appears, default `true` */
  lockScroll?: boolean;
  /** Custom class name for `Dialog` */
  class?: string;
  /** Custom style for `Dialog` */
  style?: CSSProperties;
  /** Delay time for `Dialog` opening, in milliseconds, default `0` */
  openDelay?: number;
  /** Delay time for `Dialog` closing, in milliseconds, default `0` */
  closeDelay?: number;
  /** Whether `Dialog` can be closed by clicking `modal`, default `true` */
  closeOnClickModal?: boolean;
  /** Whether `Dialog` can be closed by pressing `ESC`, default `true` */
  closeOnPressEscape?: boolean;
  /** Whether to show close button, default `true` */
  showClose?: boolean;
  /** Callback before closing, will pause `Dialog` closing. The dialog is actually closed when the `done` parameter method is executed in the callback function */
  beforeClose?: (done: DoneFn) => void;
  /** Enable draggable functionality for `Dialog`, default `false` */
  draggable?: boolean;
  /** Whether to center align the `header` and `footer` parts of `Dialog`, default `false` */
  center?: boolean;
  /** Whether to horizontally and vertically align the dialog, default `false` */
  alignCenter?: boolean;
  /** When closing `Dialog`, destroy the elements within it, default `false` */
  destroyOnClose?: boolean;
};

//element-plus.org/en-US/component/popconfirm.html#attributes
type Popconfirm = {
  /** Title */
  title?: string;
  /** Confirm button text */
  confirmButtonText?: string;
  /** Cancel button text */
  cancelButtonText?: string;
  /** Confirm button type, default `primary` */
  confirmButtonType?: ButtonType;
  /** Cancel button type, default `text` */
  cancelButtonType?: ButtonType;
  /** Custom icon, default `QuestionFilled` */
  icon?: string | Component;
  /** `Icon` color, default `#f90` */
  iconColor?: string;
  /** Whether to hide `Icon`, default `false` */
  hideIcon?: boolean;
  /** Delay when closing, default `200` */
  hideAfter?: number;
  /** Whether to insert the dropdown list of `popover` into the `body` element, default `true` */
  teleported?: boolean;
  /** When the `popover` component is not triggered for a long time and the `persistent` attribute is set to `false`, the `popover` will be deleted, default `false` */
  persistent?: boolean;
  /** Popup width, minimum width `150px`, default `150` */
  width?: string | number;
};

type BtnClickDialog = {
  options?: DialogOptions;
  index?: number;
};
type BtnClickButton = {
  btn?: ButtonProps;
  index?: number;
};
/** https://element-plus.org/en-US/component/button.html#button-attributes */
type ButtonProps = {
  /** Button text */
  label: string;
  /** Button size */
  size?: "large" | "default" | "small";
  /** Button type */
  type?: "primary" | "success" | "warning" | "danger" | "info";
  /** Whether it's a plain button, default `false` */
  plain?: boolean;
  /** Whether it's a text button, default `false` */
  text?: boolean;
  /** Whether to show text button background color, default `false` */
  bg?: boolean;
  /** Whether it's a link button, default `false` */
  link?: boolean;
  /** Whether it's a round button, default `false` */
  round?: boolean;
  /** Whether it's a circle button, default `false` */
  circle?: boolean;
  /** `Popconfirm` bubble confirmation box related configuration for confirm button */
  popconfirm?: Popconfirm;
  /** Whether it's in loading state, default `false` */
  loading?: boolean;
  /** Custom loading state icon component */
  loadingIcon?: string | Component;
  /** Whether button is disabled, default `false` */
  disabled?: boolean;
  /** Icon component */
  icon?: string | Component;
  /** Whether to enable native `autofocus` attribute, default `false` */
  autofocus?: boolean;
  /** Native `type` attribute, default `button` */
  nativeType?: "button" | "submit" | "reset";
  /** Automatically insert space between two Chinese characters */
  autoInsertSpace?: boolean;
  /** Custom button color, and automatically calculate colors after `hover` and `active` triggers */
  color?: string;
  /** `dark` mode, means automatically set `color` to dark mode color, default `false` */
  dark?: boolean;
  /** Custom element tag */
  tag?: string | Component;
  /** Callback triggered after clicking the button */
  btnClick?: ({
    dialog,
    button
  }: {
    /** Current `Dialog` information */
    dialog: BtnClickDialog;
    /** Current `button` information */
    button: BtnClickButton;
  }) => void;
};

interface DialogOptions extends DialogProps {
  /** `props` for content area component, can be received through `defineProps` */
  props?: any;
  /** Whether to hide the content of `Dialog` button operation area */
  hideFooter?: boolean;
  /** `Popconfirm` bubble confirmation box related configuration for confirm button */
  popconfirm?: Popconfirm;
  /** Whether to enable `loading` animation after clicking confirm button */
  sureBtnLoading?: boolean;
  /**
   * @description Custom dialog title content renderer
   * @see {@link https://element-plus.org/en-US/component/dialog.html#custom-header}
   */
  headerRenderer?: ({
    close,
    titleId,
    titleClass
  }: {
    close: Function;
    titleId: string;
    titleClass: string;
  }) => VNode | Component;
  /** Custom content renderer */
  contentRenderer?: ({
    options,
    index
  }: {
    options: DialogOptions;
    index: number;
  }) => VNode | Component;
  /** Custom button operation area content renderer, will override `footerButtons` and default `Cancel` and `Confirm` buttons */
  footerRenderer?: ({
    options,
    index
  }: {
    options: DialogOptions;
    index: number;
  }) => VNode | Component;
  /** Custom footer button operations */
  footerButtons?: Array<ButtonProps>;
  /** Callback after `Dialog` opens */
  open?: ({
    options,
    index
  }: {
    options: DialogOptions;
    index: number;
  }) => void;
  /** Callback after `Dialog` closes (only triggered when clicking the top-right close button or blank area or pressing esc key to close the page) */
  close?: ({
    options,
    index
  }: {
    options: DialogOptions;
    index: number;
  }) => void;
  /** Callback after `Dialog` closes. `args` returned `command` value explanation: `cancel` click cancel button, `sure` click confirm button, `close` click top-right close button or blank area or press esc key */
  closeCallBack?: ({
    options,
    index,
    args
  }: {
    options: DialogOptions;
    index: number;
    args: any;
  }) => void;
  /** Callback when clicking fullscreen button */
  fullscreenCallBack?: ({
    options,
    index
  }: {
    options: DialogOptions;
    index: number;
  }) => void;
  /** Callback when input focus is focused on `Dialog` content */
  openAutoFocus?: ({
    options,
    index
  }: {
    options: DialogOptions;
    index: number;
  }) => void;
  /** Callback when input focus loses focus from `Dialog` content */
  closeAutoFocus?: ({
    options,
    index
  }: {
    options: DialogOptions;
    index: number;
  }) => void;
  /** Callback for clicking the bottom cancel button, will pause `Dialog` closing. The dialog is actually closed when the `done` parameter method is executed in the callback function */
  beforeCancel?: (
    done: Function,
    {
      options,
      index
    }: {
      options: DialogOptions;
      index: number;
    }
  ) => void;
  /** Callback for clicking the bottom confirm button, will pause `Dialog` closing. The dialog is actually closed when the `done` parameter method is executed in the callback function */
  beforeSure?: (
    done: Function,
    {
      options,
      index,
      closeLoading
    }: {
      options: DialogOptions;
      index: number;
      /** Close the `loading` animation of confirm button */
      closeLoading: Function;
    }
  ) => void;
}

export type { EventType, ArgsType, DialogProps, ButtonProps, DialogOptions };
