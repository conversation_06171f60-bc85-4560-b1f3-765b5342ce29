import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const spaceRules = reactive<FormRules>({
  name: [
    { required: true, message: "Vui lòng nhập tên công ty", trigger: "blur" }
  ],
  email: [
    { required: true, message: "<PERSON><PERSON> lòng nhập email", trigger: "blur" },
    {
      type: "email",
      message: "Vui lòng nhập đúng định dạng email",
      trigger: ["blur"]
    }
  ]
});

export { spaceRules };
