const Layout = () => import("@/layout/index.vue");

export default {
  path: "/spaces/management",
  name: "SpaceManagement",
  redirect: "/spaces",
  component: Layout,
  meta: {
    icon: "ri/robot-2-line",
    title: "Space Management",
    rank: 4
  },
  children: [
    {
      path: "/spaces",
      name: "SpaceList",
      component: () => import("@/views/space/index.vue"),
      meta: {
        title: "My Teams",
        showLink: true
      }
    },
    {
      path: "/spaces/:slug",
      name: "SpaceDetail",
      component: () => import("@/views/space/space.vue"),
      meta: {
        title: "My Teams",
        showLink: false,
        activePath: "/spaces"
      }
    },
    {
      path: "/spaces/:slug/agent",
      name: "SpaceAgentList",
      component: () => import("@/views/space/agent.vue"),
      meta: {
        title: "My Teams",
        showLink: false,
        activePath: "/spaces"
      }
    },
    {
      path: "/spaces/:slug/agents/:agentId",
      name: "SpaceAgentDetail",
      component: () => import("@/views/space/agent-detail.vue"),
      meta: {
        title: "Agent Detail",
        showLink: false,
        activePath: "/spaces"
      }
    }
  ]
} satisfies RouteConfigsTable;
