const Layout = () => import("@/layout/index.vue");

export default {
  path: "/model-ai",
  name: "ModelAI",
  component: Layout,
  redirect: "/model-ai/provider",
  meta: {
    icon: "ri/robot-line",
    title: "AI Model Management",
    rank: 3
  },
  children: [
    {
      path: "/model-ai/provider",
      name: "Provider",
      component: () => import("@/views/model-ai/provider/index.vue"),
      meta: {
        title: "Provider Management",
        showLink: true,
        auths: ["provider:list"]
      }
    },
    {
      path: "/model-ai/categories",
      name: "ModelCategories",
      component: () => import("@/views/model-ai/categories/index.vue"),
      meta: {
        title: "Model Categories",
        showLink: true,
        auths: ["model-category:list"]
      }
    },
    {
      path: "/model-ai/ai",
      name: "ModelAiList",
      component: () => import("@/views/model-ai/ai/index.vue"),
      meta: {
        title: "Model AI",
        showLink: true,
        auths: ["model-ai:list"]
      }
    },
    {
      path: "/model-ai/tools",
      name: "ModelTools",
      component: () => import("@/views/model-ai/tools/index.vue"),
      meta: {
        title: "Model Tools",
        showLink: true,
        auths: ["model-tool:list"]
      }
    }
  ]
} satisfies RouteConfigsTable;
