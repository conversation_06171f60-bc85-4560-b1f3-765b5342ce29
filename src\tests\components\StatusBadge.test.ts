import { describe, it, expect } from "vitest";
import { mount } from "@vue/test-utils";
import StatusBadge from "@/components/Shared/StatusBadge.vue";

describe("StatusBadge", () => {
  it("renders with default props", () => {
    const wrapper = mount(StatusBadge, {
      props: {
        status: "active"
      }
    });

    expect(wrapper.exists()).toBe(true);
    expect(wrapper.text()).toContain("Active");
  });

  it("applies correct color for bot status", () => {
    const wrapper = mount(StatusBadge, {
      props: {
        status: "active",
        type: "bot"
      }
    });

    const tag = wrapper.findComponent({ name: "ElTag" });
    expect(tag.props("type")).toBe("success");
  });

  it("shows correct icon for status", () => {
    const wrapper = mount(StatusBadge, {
      props: {
        status: "draft",
        type: "bot"
      }
    });

    expect(wrapper.text()).toContain("📝");
    expect(wrapper.text()).toContain("Draft");
  });

  it("handles different status types", () => {
    const statuses = [
      { status: "active", type: "user", expectedColor: "success" },
      { status: "banned", type: "bot", expectedColor: "danger" },
      { status: "pending", type: "default", expectedColor: "warning" }
    ];

    statuses.forEach(({ status, type, expectedColor }) => {
      const wrapper = mount(StatusBadge, {
        props: { status, type }
      });

      const tag = wrapper.findComponent({ name: "ElTag" });
      expect(tag.props("type")).toBe(expectedColor);
    });
  });

  it("emits close event when closable", async () => {
    const wrapper = mount(StatusBadge, {
      props: {
        status: "active",
        closable: true
      }
    });

    const tag = wrapper.findComponent({ name: "ElTag" });
    await tag.vm.$emit("close");

    expect(wrapper.emitted("close")).toBeTruthy();
  });

  it("applies size prop correctly", () => {
    const wrapper = mount(StatusBadge, {
      props: {
        status: "active",
        size: "large"
      }
    });

    const tag = wrapper.findComponent({ name: "ElTag" });
    expect(tag.props("size")).toBe("large");
  });

  it("applies effect prop correctly", () => {
    const wrapper = mount(StatusBadge, {
      props: {
        status: "active",
        effect: "dark"
      }
    });

    const tag = wrapper.findComponent({ name: "ElTag" });
    expect(tag.props("effect")).toBe("dark");
  });
});
