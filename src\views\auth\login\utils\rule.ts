import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";
import { useUserStoreHook } from "@/store/modules/user";
import { useSettingStoreHook } from "@/store/modules/settings";

// Custom username/email validator
const usernameValidator = (_rule: any, value: string, callback: Function) => {
  if (!value || value.trim() === "") {
    callback(new Error($t("Username/email required")));
    return;
  }

  // Check if it's email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (emailRegex.test(value)) {
    // Valid email
    callback();
    return;
  }

  // Check username format
  const minLength = 3;
  const maxLength = 20;

  if (value.length < minLength) {
    callback(
      new Error($t("Username too short (min {count})", { count: minLength }))
    );
    return;
  }

  if (value.length > maxLength) {
    callback(
      new Error($t("Username too long (max {count})", { count: maxLength }))
    );
    return;
  }

  // Check for valid username characters
  if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
    callback(new Error($t("Invalid username format")));
    return;
  }

  callback();
};

// Custom password validator with dynamic minLength
const passwordValidator = (_rule: any, value: string, callback: Function) => {
  if (!value || value.trim() === "") {
    callback(new Error($t("Password is required")));
    return;
  }

  // Get dynamic settings with proper structure
  const settingStore = useSettingStoreHook();
  const security = settingStore.settings?.security || {};
  const minLength = security.passwordMinLength || 6;
  const requireNumbers = security.passwordRequireNumbers || false;
  const requireSymbols = security.passwordRequireSymbols || false;
  const requireUppercase = security.passwordRequireUppercase || false;
  const requireLowercase = security.passwordRequireLowercase || false;

  console.log("-------------------->", security);

  // Check minimum length
  if (value.length < minLength) {
    callback(
      new Error($t("Password too short (min {count})", { count: minLength }))
    );
    return;
  }

  // Check for numbers if required
  if (requireNumbers && !/\d/.test(value)) {
    callback(new Error($t("Password needs numbers")));
    return;
  }

  // Check for symbols if required
  if (requireSymbols && !/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
    callback(new Error($t("Password needs symbols")));
    return;
  }

  // Check for uppercase if required
  if (requireUppercase && !/[A-Z]/.test(value)) {
    callback(new Error($t("Password needs uppercase")));
    return;
  }

  // Check for lowercase if required
  if (requireLowercase && !/[a-z]/.test(value)) {
    callback(new Error($t("Password needs lowercase")));
    return;
  }

  callback();
};

const loginRules = reactive<FormRules>({
  login: [
    {
      validator: usernameValidator,
      trigger: "blur"
    }
  ],
  password: [
    {
      validator: passwordValidator,
      trigger: "blur"
    }
  ],
  verifyCode: [
    {
      required: true,
      message: $t("Verification code required"),
      trigger: "blur"
    },
    {
      validator: (_rule, value, callback) => {
        if (value === "") {
          callback(new Error($t("Verification code required")));
        } else if (useUserStoreHook().verifyCode !== value) {
          callback(new Error($t("Verification code incorrect")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});

export { loginRules };
