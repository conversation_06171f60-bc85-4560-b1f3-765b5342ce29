export interface FormItemProps {
  id?: number;
  title: string;
  content: string;
  type: "info" | "success" | "warning" | "error" | "announcement";
  priority: "low" | "medium" | "high" | "urgent";
  targetType: "all" | "role" | "user" | "group";
  targetIds?: number[];
  scheduledAt?: string;
  expiresAt?: string;
  isRead?: boolean;
  isActive?: boolean;
  status: "draft" | "scheduled" | "sent" | "expired" | "cancelled";
  metadata?: Record<string, any>;
  createdAt?: string;
  updatedAt?: string;
}

export interface NotificationFilterProps {
  title?: string;
  type?: string;
  priority?: string;
  targetType?: string;
  status?: string;
  isRead?: boolean;
  isActive?: boolean;
  isTrashed?: "yes" | "no";
  dateRange?: [string, string];
}

export interface NotificationListItem {
  id: number;
  title: string;
  content: string;
  type: "info" | "success" | "warning" | "error" | "announcement";
  priority: "low" | "medium" | "high" | "urgent";
  targetType: "all" | "role" | "user" | "group";
  targetIds?: number[];
  scheduledAt?: string;
  expiresAt?: string;
  isRead: boolean;
  isActive: boolean;
  status: "draft" | "scheduled" | "sent" | "expired" | "cancelled";
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  readAt?: string;
  sentAt?: string;
  recipientCount?: number;
  readCount?: number;
}

export interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<string, number>;
  byPriority: Record<string, number>;
  byStatus: Record<string, number>;
}

export interface NotificationRecipient {
  id: number;
  notificationId: number;
  userId: number;
  isRead: boolean;
  readAt?: string;
  user?: {
    id: number;
    username: string;
    firstName?: string;
    lastName?: string;
    email: string;
    avatar?: string;
  };
}

export interface NotificationTemplate {
  id: number;
  name: string;
  title: string;
  content: string;
  type: "info" | "success" | "warning" | "error" | "announcement";
  variables?: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
