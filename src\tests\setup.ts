// Test setup file
import { config } from "@vue/test-utils";
import { createI18n } from "vue-i18n";
import ElementPlus from "element-plus";

// Mock i18n
const i18n = createI18n({
  legacy: false,
  locale: "en",
  messages: {
    en: {
      "Active": "Active",
      "Draft": "Draft",
      "Banned": "Banned",
      "Pending": "Pending"
    }
  }
});

// Global test configuration
config.global.plugins = [i18n, ElementPlus];

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));
