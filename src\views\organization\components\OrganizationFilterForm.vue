<template>
  <plus-search
    v-model="filterRef"
    :columns="filterColumns"
    :show-number="false"
    label-width="100px"
    @search="handleFilter"
    @reset="handleFilter"
  />
</template>

<script setup lang="ts">
import type { OrganizationFilterProps } from "@/views/organization/utils/type";
import type { PlusColumn } from "plus-pro-components";

interface Props {
  organizationHook: any;
}

const props = defineProps<Props>();

const {
  filterRef,
  handleFilter
} = props.organizationHook;

const filterColumns: PlusColumn[] = [
  {
    label: "Name",
    prop: "name",
    valueType: "text",
    fieldProps: {
      placeholder: "Search by organization name"
    }
  },
  {
    label: "Email",
    prop: "email",
    valueType: "text",
    fieldProps: {
      placeholder: "Search by email"
    }
  },
  {
    label: "Status",
    prop: "status",
    valueType: "select",
    options: [
      { label: "All", value: "" },
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
      { label: "Suspended", value: "suspended" }
    ],
    fieldProps: {
      placeholder: "Select status"
    }
  },
  {
    label: "Organization Type",
    prop: "organizationType",
    valueType: "select",
    options: [
      { label: "All", value: "" },
      { label: "Company", value: "company" },
      { label: "Non-profit", value: "nonprofit" },
      { label: "Government", value: "government" },
      { label: "Educational", value: "educational" }
    ],
    fieldProps: {
      placeholder: "Select type"
    }
  },
  {
    label: "Industry",
    prop: "industry",
    valueType: "text",
    fieldProps: {
      placeholder: "Search by industry"
    }
  },
  {
    label: "Country",
    prop: "country",
    valueType: "text",
    fieldProps: {
      placeholder: "Search by country"
    }
  },
  {
    label: "Is Verified",
    prop: "isVerified",
    valueType: "select",
    options: [
      { label: "All", value: "" },
      { label: "Verified", value: true },
      { label: "Not Verified", value: false }
    ],
    fieldProps: {
      placeholder: "Select verification status"
    }
  },
  {
    label: "Trashed",
    prop: "isTrashed",
    valueType: "select",
    options: [
      { label: "No", value: "no" },
      { label: "Yes", value: "yes" }
    ],
    fieldProps: {
      placeholder: "Show trashed items"
    }
  }
];
</script>
