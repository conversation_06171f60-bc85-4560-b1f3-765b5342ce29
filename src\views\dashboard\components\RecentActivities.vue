<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getRecentActivities } from "../utils/api";
import { $t } from "@/plugins/i18n";
import dayjs from "dayjs";
import { ElAvatar } from "element-plus";
import { h } from "vue";

interface Activity {
  id: number;
  type: string;
  description: string;
  user: {
    name: string;
    avatar?: string;
  };
  createdAt: string;
  metadata?: any;
}

const loading = ref(false);
const activities = ref<Activity[]>([]);

const loadActivities = async () => {
  loading.value = true;
  try {
    const { data } = await getRecentActivities({ limit: 10 });
    if (data?.success) {
      activities.value = data.data || [];
    }
  } catch (error) {
    console.error("Error loading activities:", error);
  } finally {
    loading.value = false;
  }
};

const getActivityIcon = (type: string) => {
  const iconMap = {
    'bot.created': 'ri/robot-2-line',
    'bot.updated': 'ri/edit-line',
    'chat.created': 'ri/chat-3-line',
    'message.sent': 'ri/message-3-line',
    'user.login': 'ri/login-circle-line',
    'user.registered': 'ri/user-add-line'
  };
  return iconMap[type] || 'ri/information-line';
};

const getActivityColor = (type: string) => {
  const colorMap = {
    'bot.created': 'success',
    'bot.updated': 'warning',
    'chat.created': 'primary',
    'message.sent': 'info',
    'user.login': 'success',
    'user.registered': 'primary'
  };
  return colorMap[type] || 'info';
};

onMounted(() => {
  loadActivities();
});
</script>

<template>
  <el-card class="recent-activities" shadow="hover">
    <template #header>
      <div class="flex items-center justify-between">
        <span class="text-lg font-semibold">{{ $t("Recent Activities") }}</span>
        <el-button 
          type="text" 
          size="small"
          @click="loadActivities"
          :loading="loading"
        >
          {{ $t("Refresh") }}
        </el-button>
      </div>
    </template>

    <div v-loading="loading" class="activities-list">
      <div 
        v-for="activity in activities" 
        :key="activity.id"
        class="activity-item"
      >
        <div class="flex items-start space-x-3">
          <el-avatar 
            :size="32"
            :src="activity.user.avatar"
            :alt="activity.user.name"
          >
            {{ activity.user.name.charAt(0).toUpperCase() }}
          </el-avatar>
          
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-2">
              <el-tag 
                :type="getActivityColor(activity.type)"
                size="small"
                effect="plain"
              >
                <IconifyIconOffline 
                  :icon="useRenderIcon(getActivityIcon(activity.type))"
                  class="mr-1"
                />
                {{ activity.type.replace('.', ' ').toUpperCase() }}
              </el-tag>
            </div>
            
            <p class="text-sm text-gray-900 mt-1">
              <strong>{{ activity.user.name }}</strong>
              {{ activity.description }}
            </p>
            
            <p class="text-xs text-gray-500 mt-1">
              {{ dayjs(activity.createdAt).fromNow() }}
            </p>
          </div>
        </div>
      </div>

      <div v-if="!loading && activities.length === 0" class="text-center py-8">
        <div class="text-gray-400 text-sm">
          {{ $t("No recent activities") }}
        </div>
      </div>
    </div>
  </el-card>
</template>

<style scoped>
.recent-activities {
  height: 100%;
}

.activities-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover {
  background-color: #fafafa;
  margin: 0 -16px;
  padding: 12px 16px;
  border-radius: 4px;
}
</style>
