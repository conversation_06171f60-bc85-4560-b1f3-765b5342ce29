<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useNotificationHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import TableOperations from "@/components/TableOperations.vue";
import PureTable from "@pureadmin/table";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { hasAuth } from "@/router/utils";

// Lazy load components
const NotificationDrawerForm = defineAsyncComponent(
  () => import("./components/NotificationDrawerForm.vue")
);

const NotificationFilterForm = defineAsyncComponent(
  () => import("./components/NotificationFilterForm.vue")
);

const NotificationStats = defineAsyncComponent(
  () => import("./components/NotificationStats.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  stats,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetNotifications,
  fnGetNotificationStats,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  notificationFormRef,
  handleSubmit,
  handleFilter,
  handleBulkDestroy,
  handleBulkRestore,
  handleDestroy,
  handleRestore,
  handleSend,
  handleMarkAsRead,
  handleBulkMarkAsRead
} = useNotificationHook();

onMounted(() => {
  nextTick(() => {
    fnGetNotifications();
    fnGetNotificationStats();
  });
});
</script>

<template>
  <div class="main">
    <!-- Stats Dashboard -->
    <NotificationStats :stats="stats" class="mb-6" />

    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Notification Management')"
        :columns="columns"
        @refresh="fnGetNotifications"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('notification.create')"
              @click="
                () => {
                  drawerVisible = true;
                }
              "
            >
              <IconifyIconOnline
                :icon="
                  hasAuth('notification.create')
                    ? 'flat-color-icons:plus'
                    : 'icons8:plus'
                "
                width="18px"
              />
            </el-button>
          </el-tooltip>

          <template v-if="filterRef.isTrashed === 'yes'">
            <el-tooltip :content="$t('Restore')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 &&
                    !hasAuth('notification.restore'))
                "
                @click="handleBulkRestore"
              >
                <IconifyIconOnline
                  icon="tabler:restore"
                  width="18px"
                  :class="{ 'text-blue-600': multipleSelection.length > 0 }"
                />
              </el-button>
            </el-tooltip>
            <el-tooltip :content="$t('Bulk Delete')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 &&
                    !hasAuth('notification.force-delete'))
                "
                @click="handleBulkDelete"
              >
                <IconifyIconOnline
                  icon="tabler:trash-x-filled"
                  width="18px"
                  :class="{ 'text-red-700': multipleSelection.length > 0 }"
                />
              </el-button>
            </el-tooltip>
          </template>
          <template v-else>
            <el-tooltip :content="$t('Mark as Read')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 &&
                    !hasAuth('notification.read'))
                "
                @click="handleBulkMarkAsRead"
              >
                <IconifyIconOnline
                  icon="tabler:mail-opened"
                  width="18px"
                  :class="{ 'text-green-600': multipleSelection.length > 0 }"
                />
              </el-button>
            </el-tooltip>
            <el-tooltip
              v-if="filterRef.isTrashed === 'no'"
              :content="$t('Bulk Destroy')"
              placement="top"
            >
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length == 0 ||
                  (multipleSelection.length > 0 &&
                    !hasAuth('notification.destroy'))
                "
                @click="handleBulkDestroy"
              >
                <IconifyIconOnline
                  icon="tabler:trash"
                  width="18px"
                  :class="{ 'text-red-700': multipleSelection.length > 0 }"
                />
              </el-button>
            </el-tooltip>
          </template>
        </template>

        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            border
            align-whole="center"
            showOverflowTooltip
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :paginationSmall="size === 'small' ? true : false"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="fnHandleSelectionChange"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
          >
            <template #operation="{ row }">
              <TableOperations
                :row="row"
                :is-trashed="filterRef.isTrashed"
                :permissions="{
                  edit: 'notification.edit',
                  destroy: 'notification.destroy',
                  restore: 'notification.restore',
                  forceDelete: 'notification.force-delete'
                }"
                @edit="
                  () => {
                    drawerValues = clone(row, true);
                    drawerVisible = true;
                  }
                "
                @destroy="handleDestroy"
                @restore="handleRestore"
                @delete="handleDelete"
              >
                <template #extra-actions>
                  <el-tooltip
                    v-if="
                      row.status === 'draft' && hasAuth('notification.send')
                    "
                    :content="$t('Send')"
                    placement="top"
                  >
                    <el-button
                      type="text"
                      size="small"
                      class="reset-margin"
                      @click="handleSend(row)"
                    >
                      <IconifyIconOnline
                        icon="tabler:send"
                        width="16px"
                        class="text-blue-600"
                      />
                    </el-button>
                  </el-tooltip>
                  <el-tooltip
                    v-if="!row.isRead && hasAuth('notification.read')"
                    :content="$t('Mark as Read')"
                    placement="top"
                  >
                    <el-button
                      type="text"
                      size="small"
                      class="reset-margin"
                      @click="handleMarkAsRead(row)"
                    >
                      <IconifyIconOnline
                        icon="tabler:mail-opened"
                        width="16px"
                        class="text-green-600"
                      />
                    </el-button>
                  </el-tooltip>
                </template>
              </TableOperations>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <!-- Filter Form -->
    <NotificationFilterForm
      v-model:visible="filterVisible"
      :data="filterRef"
      @confirm="handleFilter"
    />

    <!-- Drawer Form -->
    <NotificationDrawerForm
      ref="notificationFormRef"
      v-model:visible="drawerVisible"
      :data="drawerValues"
      @confirm="handleSubmit"
    />
  </div>
</template>
