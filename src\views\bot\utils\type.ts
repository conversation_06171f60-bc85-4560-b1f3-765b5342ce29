export type FormItemProps = {
  id?: number | null;
  uuid?: string;
  name?: string;
  logo?: string;
  description?: string;
  ownerId?: number;
  ownerType?: string;
  owner?: any;
  aiModelId?: number;
  aiModel?: any;
  systemPrompt?: string;
  greetingMessage?: string;
  starterMessages?: any;
  closingMessage?: string;
  parameters?: any;
  toolCallingMode?: "auto" | "none" | "required";
  visibility?: string;
  botType?: string;
  status?: "draft" | "review" | "active" | "paused" | "banned";
  metadata?: any;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
};

export type BotFilterProps = {
  name?: string;
  status?: "draft" | "review" | "active" | "paused" | "banned";
  visibility?: string;
  botType?: string;
  ownerId?: number;
  ownerType?: string;
  aiModelId?: number;
  toolCallingMode?: "auto" | "none" | "required";
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
