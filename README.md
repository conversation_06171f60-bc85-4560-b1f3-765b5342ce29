<h1>vue-pure-admin Thin Version (Non-internationalized)</h1>

[![license](https://img.shields.io/github/license/pure-admin/vue-pure-admin.svg)](LICENSE)

**English** | [中文](./README.zh-CN.md)

## Introduction

The thin version is a streamlined framework extracted from [vue-pure-admin](https://github.com/pure-admin/vue-pure-admin), containing core functionality and more suitable for actual project development. The bundle size remains under `2.3MB` even with global import of [element-plus](https://element-plus.org), and it will permanently sync with the full version code. With `brotli` compression and `cdn` replacement for local libraries enabled, the bundle size is under `350kb`.

## Version Selection

This is the non-internationalized version. If you need the internationalized version, [please click here](https://github.com/pure-admin/pure-admin-thin/tree/i18n)

## Tutorial Videos

[View UI Design Tutorial](https://www.bilibili.com/video/BV17g411T7rq)
[View Quick Development Tutorial](https://www.bilibili.com/video/BV1kg411v7QT)

## Comprehensive Documentation

[View vue-pure-admin Documentation](https://pure-admin.cn/)
[View @pureadmin/utils Documentation](https://pure-admin-utils.netlify.app)

## Premium Services

[View Details](https://pure-admin.cn/pages/service/)

## Preview

[View Preview](https://pure-admin-thin.netlify.app/#/login)

## Maintainer

[xiaoxian521](https://github.com/xiaoxian521)

## ⚠️ Notice

The thin version does not accept any `issues` or `pr`. If you have problems, please go to the full version [issues](https://github.com/pure-admin/vue-pure-admin/issues/new/choose) to submit them. Thank you!

## License

[MIT © 2020-present, pure-admin](./LICENSE)
