<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import { message } from "@/utils/message";
import { resetPasswordRules } from "./utils/rule";
import { ref, reactive, computed, onMounted, toRaw } from "vue";
import { debounce } from "@pureadmin/utils";
import { useEventListener } from "@vueuse/core";
import type { FormInstance } from "element-plus";
import { $t } from "@/plugins/i18n";
import { resetPassword, type UserResult } from "@/views/auth";
import { useUserStoreHook } from "@/store/modules/user";
import Motion from "./utils/motion";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { bg, avatar, illustration } from "./utils/static";
import { useTranslation } from "@/layout/hooks/useTranslation";
import { useLanguageStoreHook } from "@/store/modules/language";
import { useSettingStoreHook } from "@/store/modules/settings";
import i18n from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import AuthNavigation from "../components/AuthNavigation.vue";

import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import Lock from "@iconify-icons/ri/lock-fill";
import TypeIt from "@/components/ReTypeit/src";
import globalization from "@/assets/svg/globalization.svg?component";

defineOptions({
  name: "ResetPassword"
});

const router = useRouter();
const route = useRoute();
const loading = ref(false);
const resendLoading = ref(false);
const ruleFormRef = ref<FormInstance>();
const countdown = ref(0);
const countdownTimer = ref<NodeJS.Timeout | null>(null);

const { dataTheme, dataThemeChange } = useDataThemeChange();
const translation = useTranslation();
const useLanguage = useLanguageStoreHook();
const useSetting = useSettingStoreHook();

const disabled = computed(() => loading.value || resendLoading.value);
const hasInteracted = ref(false);

const languages = computed(() => {
  return useLanguage.languages?.map((language: any) => ({
    locale: language.code,
    native: language.nativeName
  }));
});

const currentLocale = computed(() => {
  return useLanguage.locale || i18n.global.locale.value;
});

const ruleForm = reactive({
  email: "",
  otp: "",
  password: "",
  confirmPassword: ""
});

const canResend = computed(() => {
  return countdown.value === 0 && !resendLoading.value;
});

const getDropdownItemStyle = (currentLocale: string, locale: string) => {
  return {
    color: currentLocale === locale ? "#409eff" : ""
  };
};

const getDropdownItemClass = (currentLocale: string, locale: string) => {
  return currentLocale === locale ? "!font-medium" : "";
};

const handleSwitch = (locale: string) => {
  translation.switchLanguage(locale);
};

const handleInputFocus = () => {
  hasInteracted.value = true;
};

onMounted(() => {
  // Get email from query params
  if (route.query.email) {
    ruleForm.email = route.query.email as string;
  }

  // Start countdown for resend
  startCountdown();
});

const startCountdown = () => {
  countdown.value = 60;
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!);
      countdownTimer.value = null;
    }
  }, 1000);
};

const onResetPassword = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    // Enable validation before submit
    hasInteracted.value = true;
    await formEl.validate();
    loading.value = true;
    const res: UserResult = await resetPassword({
      email: ruleForm.email,
      code: ruleForm.otp,
      password: ruleForm.password,
      passwordConfirmation: ruleForm.confirmPassword
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    message(res.message, { type: "success" });
    // Redirect to login page
    await router.push("/login");
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Some information is incorrect. Please review and try again."),
      {
        type: "error"
      }
    );
  } finally {
    loading.value = false;
  }
};

const immediateDebounce: any = debounce(
  formRef => onResetPassword(formRef),
  1000,
  true
);

const goToLogin = () => {
  router.push("/login");
};

const onResendOTP = async () => {
  if (!canResend.value || !ruleForm.email) return;

  try {
    resendLoading.value = true;
    const res: UserResult = await useUserStoreHook().forgotPassword({
      email: ruleForm.email
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    message(res.message, { type: "success" });
    startCountdown();
  } catch (error: any) {
    message(error?.response?.data?.message || error?.message, {
      type: "error"
    });
  } finally {
    resendLoading.value = false;
  }
};

useEventListener(document, "keydown", ({ code }) => {
  if (["Enter", "NumpadEnter"].includes(code) && !loading.value)
    immediateDebounce(ruleFormRef.value);
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" alt="Bg" />
    <div class="flex-c absolute right-5 top-3">
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
      <el-dropdown trigger="click">
        <globalization
          class="hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"
        />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              v-for="lang in languages"
              :key="lang.locale"
              :style="getDropdownItemStyle(currentLocale, lang.locale)"
              :class="[
                'dark:!text-white',
                getDropdownItemClass(currentLocale, lang.locale)
              ]"
              @click="handleSwitch(lang.locale)"
            >
              {{ lang.native }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(illustration)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <h2 class="outline-none">
              <TypeIt
                :options="{
                  strings: [$t('Reset Password')],
                  cursor: false,
                  speed: 100
                }"
              />
            </h2>
          </Motion>

          <div class="text-center mb-4 text-gray-500">
            {{ $t("Enter the OTP code sent to") }}
            <strong>{{ ruleForm.email }}</strong>
          </div>

          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="hasInteracted ? resetPasswordRules(ruleForm) : {}"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item prop="otp">
                <el-input
                  v-model="ruleForm.otp"
                  class="text-center"
                  maxlength="6"
                  :placeholder="$t('OTP Code')"
                  :prefix-icon="useRenderIcon('ri:shield-keyhole-line')"
                  @focus="handleInputFocus"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item prop="password">
                <el-input
                  v-model="ruleForm.password"
                  type="password"
                  show-password
                  :placeholder="$t('New Password')"
                  :prefix-icon="useRenderIcon(Lock)"
                  @focus="handleInputFocus"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="200">
              <el-form-item prop="confirmPassword">
                <el-input
                  v-model="ruleForm.confirmPassword"
                  type="password"
                  show-password
                  :placeholder="$t('Confirm New Password')"
                  :prefix-icon="useRenderIcon(Lock)"
                  @focus="handleInputFocus"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="250">
              <el-button
                class="w-full !uppercase"
                size="large"
                type="danger"
                round
                :loading="loading"
                :disabled="disabled"
                @click="onResetPassword(ruleFormRef)"
              >
                <IconifyIconOnline
                  :icon="'ri:lock-password-line'"
                  width="20"
                  class="mr-2"
                />
                {{ $t("Reset Password") }}
              </el-button>
            </Motion>

            <Motion :delay="300">
              <div class="text-center mt-4">
                <span class="text-gray-500 text-sm">{{
                  $t("Didn't receive the code?")
                }}</span>
                <el-button
                  link
                  type="primary"
                  :disabled="!canResend"
                  :loading="resendLoading"
                  class="ml-1"
                  @click="onResendOTP"
                >
                  {{
                    canResend
                      ? $t("Resend OTP")
                      : $t("Resend in {seconds}s", { seconds: countdown })
                  }}
                </el-button>
              </div>
            </Motion>

            <Motion :delay="350">
              <AuthNavigation
                :show-login="true"
                :show-register="false"
                :show-forgot-password="false"
              />
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
:deep(.text-center input) {
  text-align: center;
  font-size: 1.2em;
  letter-spacing: 0.5em;
  font-weight: bold;
}
</style>
