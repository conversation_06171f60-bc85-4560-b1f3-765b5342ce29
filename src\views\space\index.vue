<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent, h } from "vue";
import { useSpaceHook } from "./utils/hook";
import { clone, deviceDetection } from "@pureadmin/utils";
import SpaceCard from "@/views/space/components/SpaceCard.vue";

// Lazy load components
const SpaceForm = defineAsyncComponent(
  () => import("./components/SpaceForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  records,
  // Event handlers
  fnGetSpaces,
  fnHandleSortChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  spaceFormRef,
  handleSubmit,
  handleFilter,
  handleDestroy
} = useSpaceHook();

const handleEdit = (row: any) => {
  drawerValues.value = clone(row, true);
  drawerVisible.value = true;
};

onMounted(() => {
  nextTick(() => {
    fnGetSpaces();
  });
});
</script>

<template>
  <div class="main">
    <div ref="contentRef">
      <header
        class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6"
      >
        <div>
          <h1 class="text-2xl font-bold text-gray-800">My Teams</h1>
          <p class="text-gray-500">Quản lý danh sách các công ty của bạn.</p>
        </div>
        <el-button
          type="primary"
          round
          size="large"
          class="mt-4 sm:mt-0"
          @click="drawerVisible = true"
        >
          <IconifyIconOnline :icon="'tabler:plus'" class="mr-2" />
          Thêm công ty
        </el-button>
      </header>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl"
      >
        <div
          class="w-full h-full min-h-[356px] rounded-2xl border-2 border-dashed border-gray-300 flex flex-col items-center justify-center text-gray-500 hover:border-indigo-500 hover:text-indigo-500 transition-colors duration-300 cursor-pointer bg-white"
          @click="drawerVisible = true"
        >
          <IconifyIconOnline icon="line-md:plus" class="text-8xl" />
          <span class="font-semibold text-lg"> Tạo Space </span>
        </div>
        <SpaceCard
          v-for="space in records"
          :key="space.id || space.uuid"
          :space="space"
          @edit="handleEdit"
          @delete="handleDestroy"
        />
      </div>
    </div>
    <SpaceForm
      ref="spaceFormRef"
      v-model:visible="drawerVisible"
      :values="drawerValues"
      @submit="handleSubmit"
    />
  </div>
</template>

<style lang="scss" scoped>
.main {
  @apply p-4;
}
</style>
