<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { usePageHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import TableOperations from "@/components/TableOperations.vue";
import PureTable from "@pureadmin/table";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

// Lazy load components
const PageDrawerForm = defineAsyncComponent(
  () => import("./components/PageDrawerForm.vue")
);

const PageFilterForm = defineAsyncComponent(
  () => import("./components/PageFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetPages,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  pageFormRef,
  handleSubmit,
  handleFilter,
  handleBulkDestroy,
  handleBulkRestore,
  handleDestroy,
  handleRestore
} = usePageHook();

onMounted(() => {
  nextTick(() => {
    fnGetPages();
  });
});

const handleEdit = (row: any) => {
  drawerValues.value = clone(row, true);
  drawerVisible.value = true;
};
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Page Management')"
        :columns="columns"
        @refresh="fnGetPages"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('page.create')"
              @click="
                () => {
                  drawerVisible = true;
                }
              "
            >
              <IconifyIconOnline
                :icon="
                  hasAuth('page.create')
                    ? 'flat-color-icons:plus'
                    : 'icons8:plus'
                "
                width="18px"
              />
            </el-button>
          </el-tooltip>
        </template>

        <template #operations>
          <TableOperations
            :multipleSelection="multipleSelection"
            :filterRef="filterRef"
            @bulk-destroy="handleBulkDestroy"
            @bulk-restore="handleBulkRestore"
            @bulk-delete="handleBulkDelete"
          />
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            border
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown split-button trigger="click" size="small">
                {{ $t("Action") }}
                <template #dropdown>
                  <el-dropdown-menu class="min-w-[130px]">
                    <el-dropdown-item disabled>
                      {{ $t("Action") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      :disabled="
                        filterRef.isTrashed == 'yes' ||
                        (filterRef.isTrashed == 'no' && !hasAuth('page.update'))
                      "
                      divided
                      @click="handleEdit(row)"
                    >
                      <IconifyIconOnline
                        icon="ant-design:edit-outlined"
                        class="text-blue-700"
                      />
                      <span class="ml-2">{{ $t("Edit") }}</span>
                    </el-dropdown-item>

                    <el-dropdown-item
                      v-if="filterRef.isTrashed === 'no'"
                      :disabled="!hasAuth('page.destroy')"
                      @click="handleDestroy(row)"
                    >
                      <IconifyIconOnline
                        icon="tabler:trash"
                        class="text-red-800"
                      />
                      <span class="ml-2">
                        {{ $t("Destroy") }}
                      </span>
                    </el-dropdown-item>

                    <template v-if="filterRef.isTrashed == 'yes'">
                      <el-dropdown-item
                        :disabled="!hasAuth('page.restore')"
                        @click="handleRestore(row)"
                      >
                        <IconifyIconOnline
                          icon="tabler:restore"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Restore") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('page.force-delete')"
                        @click="handleDelete(row)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash-x"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Force Delete") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <PageDrawerForm
      ref="pageFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          pageFormRef.value?.resetForm();
          drawerValues = { status: 'draft' };
        }
      "
    />

    <PageFilterForm
      ref="filterFormRef"
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="
        () => {
          filterRef = {};
          fnGetPages();
        }
      "
    />
  </div>
</template>

<style lang="scss" scoped>
.main {
  @apply p-4;
}
</style>
