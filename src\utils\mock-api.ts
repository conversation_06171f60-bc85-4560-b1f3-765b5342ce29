// Mock API interceptor for development
import axios from "axios";
import {
  mockUsers,
  mockAiModels,
  mockBots,
  mockChats,
  mockMessages,
  mockDashboardStats,
  mockRecentActivities,
  paginate,
  delay
} from "./mock-data";

// Enable mock mode (set to false to use real API)
export const MOCK_MODE = import.meta.env.VITE_MOCK_API === "true";

// Mock API responses
const mockResponses = {
  // Dashboard endpoints
  "GET /api/v1/auth/dashboard/stats": () => ({
    success: true,
    data: mockDashboardStats
  }),

  "GET /api/v1/auth/dashboard/recent-activities": (params: any) => {
    const { limit = 10 } = params;
    const activities = mockRecentActivities.slice(0, limit);
    return {
      success: true,
      data: activities
    };
  },

  // Chatbot Dashboard endpoints
  "GET /api/v1/auth/dashboard/chatbot-stats": () => ({
    success: true,
    data: {
      chatbotStats: {
        totalBots: 12,
        activeBots: 8,
        draftBots: 4,
        mostUsedBot: "Customer Support Bot"
      },
      conversationStats: {
        totalConversations: 245,
        todayConversations: 18,
        weekConversations: 89,
        avgConversationLength: 12.5
      },
      messageStats: {
        totalMessages: 3420,
        todayMessages: 156,
        avgResponseTime: 1.2,
        messageTypes: {
          text: 2890,
          file: 380,
          image: 150
        }
      },
      tokenStats: {
        totalTokens: 125680,
        inputTokens: 45230,
        outputTokens: 80450,
        todayTokens: 1250,
        estimatedCost: 15.75,
        avgPerConversation: 340
      },
      knowledgeStats: {
        totalDocuments: 45,
        totalPages: 1250,
        storageUsed: 125.5,
        fileTypes: {
          pdf: 25,
          docx: 12,
          txt: 8,
          other: 5
        },
        mostQueriedDoc: "Product Manual.pdf"
      },
      storageStats: {
        totalUsed: 245.8,
        documentsSize: 125.5,
        attachmentsSize: 120.3,
        remainingQuota: 754.2,
        quotaLimit: 1000
      }
    }
  }),

  "GET /api/v1/auth/dashboard/token-trend": (params: any) => {
    const { days = 7 } = params;
    const data = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split('T')[0],
        inputTokens: Math.floor(Math.random() * 1000) + 1000,
        outputTokens: Math.floor(Math.random() * 1500) + 1500
      });
    }
    return {
      success: true,
      data
    };
  },

  "GET /api/v1/auth/dashboard/conversation-trend": (params: any) => {
    const { days = 7 } = params;
    const data = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split('T')[0],
        conversations: Math.floor(Math.random() * 20) + 10
      });
    }
    return {
      success: true,
      data
    };
  },

  // Bot endpoints
  "GET /api/auth/bots": (params: any) => {
    const { page = 1, per_page = 10, sort_by = "id", sort_order = "desc" } = params;
    let data = [...mockBots];
    
    // Apply sorting
    data.sort((a, b) => {
      const aVal = a[sort_by as keyof typeof a];
      const bVal = b[sort_by as keyof typeof b];
      const order = sort_order === "asc" ? 1 : -1;
      return aVal > bVal ? order : -order;
    });

    const result = paginate(data, page, per_page);
    return {
      success: true,
      data: result
    };
  },

  "GET /api/auth/bots/:id": (params: any, pathParams: any) => {
    const bot = mockBots.find(b => b.id === parseInt(pathParams.id));
    return {
      success: !!bot,
      data: bot || null
    };
  },

  "POST /api/auth/bots": (data: any) => {
    const newBot = {
      id: Math.max(...mockBots.map(b => b.id)) + 1,
      uuid: `bot-${Date.now()}`,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockBots.push(newBot);
    return {
      success: true,
      data: newBot
    };
  },

  "PUT /api/auth/bots/:id": (data: any, pathParams: any) => {
    const index = mockBots.findIndex(b => b.id === parseInt(pathParams.id));
    if (index !== -1) {
      mockBots[index] = {
        ...mockBots[index],
        ...data,
        updatedAt: new Date().toISOString()
      };
      return {
        success: true,
        data: mockBots[index]
      };
    }
    return { success: false, message: "Bot not found" };
  },

  "DELETE /api/auth/bots/:id": (data: any, pathParams: any) => {
    const index = mockBots.findIndex(b => b.id === parseInt(pathParams.id));
    if (index !== -1) {
      mockBots[index].deletedAt = new Date().toISOString();
      return { success: true };
    }
    return { success: false, message: "Bot not found" };
  },

  // Chat endpoints
  "GET /api/auth/chat/bots": () => {
    // Trả về bots với conversation có sẵn
    return {
      success: true,
      data: mockBots
    };
  },

  "GET /api/auth/chats": (params: any) => {
    const { page = 1, per_page = 10 } = params;
    const result = paginate(mockChats, page, per_page);
    return {
      success: true,
      data: result
    };
  },

  "POST /api/auth/chats": (data: any) => {
    const newChat = {
      id: Math.max(...mockChats.map(c => c.id)) + 1,
      uuid: `chat-${Date.now()}`,
      ...data,
      messageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockChats.push(newChat);
    return {
      success: true,
      data: newChat
    };
  },

  // Message endpoints
  "GET /api/auth/messages": (params: any) => {
    const { page = 1, per_page = 20 } = params;
    const result = paginate(mockMessages, page, per_page);
    return {
      success: true,
      data: result
    };
  },

  "POST /api/auth/messages": (data: any) => {
    const newMessage = {
      id: Math.max(...mockMessages.map(m => m.id)) + 1,
      uuid: `msg-${Date.now()}`,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockMessages.push(newMessage);
    return {
      success: true,
      data: newMessage
    };
  },

  // User endpoints
  "GET /api/auth/users": () => ({
    success: true,
    data: mockUsers
  }),

  // AI Model endpoints
  "GET /api/auth/model-ai": () => ({
    success: true,
    data: mockAiModels
  })
};

// Setup mock interceptor
export const setupMockApi = () => {
  if (!MOCK_MODE) return;

  console.log("🔧 Mock API enabled");

  // Request interceptor
  axios.interceptors.request.use(async (config) => {
    const method = config.method?.toUpperCase();
    const url = config.url;
    
    if (!method || !url) return config;

    // Extract path parameters
    const pathParams: Record<string, string> = {};
    let normalizedUrl = url;
    
    // Replace path parameters with placeholders
    normalizedUrl = normalizedUrl.replace(/\/(\d+)/g, (match, id) => {
      pathParams.id = id;
      return "/:id";
    });

    const mockKey = `${method} ${normalizedUrl}`;
    const mockHandler = mockResponses[mockKey as keyof typeof mockResponses];

    if (mockHandler) {
      console.log("🎯 Mock API handling:", mockKey);

      const params = method === "GET" ? config.params : config.data;
      const response = mockHandler(params, pathParams);

      // Create custom adapter that returns mock response
      config.adapter = () => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              data: response,
              status: response.success ? 200 : 400,
              statusText: response.success ? "OK" : "Bad Request",
              headers: {},
              config
            });
          }, 300); // Simulate network delay
        });
      };
    }

    return config;
  });

  // Response interceptor for additional mock handling
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.message?.includes("Mock API")) {
        console.warn("Mock API Error:", error.message);
      }
      return Promise.reject(error);
    }
  );
};

// Initialize mock API if enabled
if (MOCK_MODE) {
  setupMockApi();
}
