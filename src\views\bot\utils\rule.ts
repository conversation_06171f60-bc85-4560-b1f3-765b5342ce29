import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const botRules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: $t("Please enter the bot name"),
      trigger: "blur"
    },
    {
      min: 1,
      max: 120,
      message: $t("Length must be between 1 and 120 characters"),
      trigger: "blur"
    }
  ],
  aiModelId: [
    {
      required: true,
      message: $t("Please select an AI model"),
      trigger: "change"
    }
  ],
  systemPrompt: [
    {
      required: true,
      message: $t("Please enter the system prompt"),
      trigger: "blur"
    }
  ],
  toolCallingMode: [
    {
      required: true,
      message: $t("Please select tool calling mode"),
      trigger: "change"
    }
  ],
  visibility: [
    {
      required: true,
      message: $t("Please select visibility"),
      trigger: "change"
    }
  ],
  botType: [
    {
      required: true,
      message: $t("Please select bot type"),
      trigger: "change"
    }
  ],
  status: [
    {
      required: true,
      message: $t("Please select status"),
      trigger: "change"
    }
  ]
});

export { botRules };
