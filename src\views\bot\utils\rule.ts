import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const botRules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: $t("Please enter AI Assistant Name"),
      trigger: "blur"
    },
    {
      min: 3,
      max: 50,
      message: $t("Length from 3 to 50 characters"),
      trigger: "blur"
    }
  ],
  model: [
    {
      required: true,
      message: $t("Please select LLM model"),
      trigger: "blur"
    }
  ],
  systemPrompt: [
    {
      required: true,
      message: $t("Please enter System Prompt"),
      trigger: "blur"
    },
    {
      min: 20,
      message: $t("Prompt needs at least 20 characters to be effective"),
      trigger: "blur"
    }
  ]
});

export { botRules };
