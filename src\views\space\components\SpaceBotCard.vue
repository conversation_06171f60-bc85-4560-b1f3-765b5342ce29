<script setup lang="ts"></script>

<template>
  <div class="group">
    <el-card
      class="transform transition-all !rounded-[12px] duration-300 hover:scale-105 hover:shadow-2xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden"
    >
      <template #header>
        <div
          class="bg-gradient-to-r from-emerald-500 to-teal-500 -m-5 mb-4 p-6 text-white"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div
                class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm"
              >
                <i class="el-icon-picture text-2xl" />
              </div>
              <div>
                <h3 class="text-xl font-bold">DALL-E Creator</h3>
                <p class="text-emerald-100 text-sm">DALL-E 3</p>
              </div>
            </div>
            <el-tag
              type="warning"
              effect="light"
              size="small"
              class="bg-yellow-100 text-yellow-800 border-yellow-200"
            >
              Beta
            </el-tag>
          </div>
        </div>
      </template>

      <div class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center space-x-2">
            <i class="el-icon-user text-gray-400" />
            <div>
              <p class="text-xs text-gray-500">Người tạo</p>
              <p class="font-semibold text-gray-800">AI Studio</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <i class="el-icon-calendar text-gray-400" />
            <div>
              <p class="text-xs text-gray-500">Ngày tạo</p>
              <p class="font-semibold text-gray-800">22/03/2024</p>
            </div>
          </div>
        </div>

        <div>
          <div
            class="text-sm text-gray-600 leading-relaxed text-justify line-clamp-4"
          >
            AI tạo hình ảnh từ mô tả văn bản với chất lượng nghệ thuật cao, hỗ
            trợ nhiều phong cách và định dạng khác nhau.
          </div>
        </div>

        <div class="grid grid-cols-3 gap-3 pt-4 border-t border-gray-100">
          <div class="text-center">
            <p class="text-2xl font-bold text-emerald-600">500K</p>
            <p class="text-xs text-gray-500">Hình ảnh</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-teal-600">4.6</p>
            <p class="text-xs text-gray-500">Đánh giá</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-green-600">95%</p>
            <p class="text-xs text-gray-500">Thành công</p>
          </div>
        </div>

        <div class="flex space-x-2 pt-2">
          <el-button
            round
            type="danger"
            size="small"
            class="flex-1 !uppercase"
            @click="useAgent('DALL-E')"
          >
            <i class="el-icon-picture-outline mr-1" />
            Vào chat
          </el-button>
          <el-button
            round
            type="text"
            size="small"
            @click="viewDetails('DALL-E')"
          >
            <i class="el-icon-view mr-1" />
            Chi tiết
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss"></style>
