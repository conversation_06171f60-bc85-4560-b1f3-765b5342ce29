import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const languageRules = reactive<FormRules>({
  code: [
    {
      required: true,
      message: $t("Please enter the language code"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 10,
      message: $t("Length must be between 2 and 10 characters"),
      trigger: "blur"
    }
  ],
  name: [
    {
      required: true,
      message: $t("Please enter the language name"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 100,
      message: $t("Length must be between 2 and 100 characters"),
      trigger: "blur"
    }
  ],
  nativeName: [
    {
      required: true,
      message: $t("Please enter the native name"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 100,
      message: $t("Length must be between 2 and 100 characters"),
      trigger: "blur"
    }
  ]
});

export { languageRules };
