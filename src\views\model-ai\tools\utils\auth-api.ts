import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/model-ai/tools/utils/type";

export const getModelTools = (params?: object) => {
  return http.request<Result>("get", "/api/auth/model-tools", {
    params
  });
};

export const getModelToolById = (id: number) => {
  return http.request<Result>("get", `/api/auth/model-tools/${id}`);
};

export const createModelTool = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/model-tools", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateModelToolById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/model-tools/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const destroyModelToolById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/model-tools/${id}`);
};

export const bulkDestroyModelTools = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/model-tools/bulk-destroy", {
    data
  });
};

export const restoreModelToolById = (id: number) => {
  return http.request<Result>("put", `/api/auth/model-tools/${id}/restore`);
};

export const bulkRestoreModelTools = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/model-tools/bulk-restore", {
    data
  });
};

export const deleteModelToolById = (id: number) => {
  return http.request<Result>(
    "delete",
    `/api/auth/model-tools/${id}/force-delete`
  );
};

export const bulkDeleteModelTools = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/model-tools/bulk-delete", {
    data
  });
};

export const dropdownModelTools = () => {
  return http.request<Result>("get", "/api/auth/model-tools/dropdown");
};

export const testModelTool = (id: number, data: { parameters: any }) => {
  return http.request<Result>("post", `/api/auth/model-tools/${id}/test`, {
    data
  });
};

export const validateModelTool = (id: number) => {
  return http.request<Result>("post", `/api/auth/model-tools/${id}/validate`);
};
