<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { loginRules } from "./utils/rule";
import { computed, reactive, ref, toRaw, watch } from "vue";
import { debounce } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";
import { useEventListener } from "@vueuse/core";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { getTopMenu, initRouter } from "@/router/utils";
import { avatar, bg, illustration } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";

import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import globalization from "@/assets/svg/globalization.svg?component";
import Lock from "@iconify-icons/ri/lock-fill";
import User from "@iconify-icons/ri/user-3-fill";
import TypeIt from "@/components/ReTypeit/src";
import ReImageVerify from "@/components/ReImageVerify/src/index.vue";
import i18n, { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import { useLanguageStoreHook } from "@/store/modules/language";
import { useSettingStoreHook } from "@/store/modules/settings";
import { useTranslation } from "@/layout/hooks/useTranslation";
import type { UserResult } from "@/views/user/utils/api";
import SocialLogin from "../components/SocialLogin.vue";

defineOptions({
  name: "Login"
});

const useLanguage = useLanguageStoreHook();
const useSetting = useSettingStoreHook();
const translation = useTranslation();

// Ensure settings are loaded if not already
if (Object.keys(useSetting.settings).length === 0) {
  useSetting.fetchPublicSettings();
}

const router = useRouter();
const loading = ref(false);
const disabled = ref(false);
const ruleFormRef = ref<FormInstance>();

const imgCode = ref("");

const { initStorage } = useLayout();
initStorage();

const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title, getDropdownItemStyle, getDropdownItemClass } = useNav();

const ruleForm = reactive({
  login: "admin",
  password: "admin123",
  verifyCode: null
});

const languages = computed(() => {
  return useLanguage.languages?.map((language: any) => ({
    locale: language.code,
    native: language.nativeName
  }));
});

const currentLocale = computed(() => {
  return useLanguage.locale || i18n.global.locale.value;
});

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    await formEl.validate();
    loading.value = true;
    const res: UserResult =
      await useUserStoreHook().loginByCredential(ruleForm);
    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }
    disabled.value = true;
    try {
      await initRouter();
      await router.push(getTopMenu(true).path);
      message(res.message, { type: "success" });
    } catch (e) {
      message($t("Router Error"), { type: "error" });
    } finally {
      disabled.value = false;
    }
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Some information is incorrect. Please review and try again."),
      {
        type: "error"
      }
    );
  } finally {
    loading.value = false;
  }
};

const immediateDebounce: any = debounce(
  formRef => onLogin(formRef),
  1000,
  true
);

const handleSwitch = (locale: string) => {
  translation.switchLanguage(locale);
};

const handleSocialLogin = async (provider: string) => {
  try {
    loading.value = true;
    // Redirect to social login URL
    const baseUrl = import.meta.env.VITE_API_BASE_URL || "";
    window.location.href = `${baseUrl}/auth/social/${provider}`;
  } catch (error: any) {
    message(error?.message || $t("Social login failed"), {
      type: "error"
    });
  } finally {
    loading.value = false;
  }
};

const goToRegister = () => {
  router.push("/register");
};

const goToForgotPassword = () => {
  router.push("/forget");
};

watch(imgCode, value => {
  useUserStoreHook().setVerifyCode(value);
});

useEventListener(document, "keydown", ({ code }) => {
  if (
    ["Enter", "NumpadEnter"].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce(ruleFormRef.value);
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" alt="Bg" />
    <div class="flex-c absolute right-5 top-3">
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
      <el-dropdown trigger="click">
        <globalization
          class="hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"
        />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              v-for="lang in languages"
              :key="lang.locale"
              :style="getDropdownItemStyle(currentLocale, lang.locale)"
              :class="[
                'dark:!text-white',
                getDropdownItemClass(currentLocale, lang.locale)
              ]"
              @click="handleSwitch(lang.locale)"
            >
              {{ lang.native }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(illustration)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <Motion>
              <h2 class="outline-none">
                <TypeIt
                  :options="{ strings: [title], cursor: false, speed: 100 }"
                />
              </h2>
            </Motion>
          </Motion>
          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="loginRules"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item prop="login">
                <el-input
                  v-model="ruleForm.login"
                  clearable
                  :placeholder="$t('Username/Email')"
                  :prefix-icon="useRenderIcon(User)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item prop="password">
                <el-input
                  v-model="ruleForm.password"
                  clearable
                  show-password
                  :placeholder="$t('Password')"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="200">
              <el-form-item prop="verifyCode">
                <el-input
                  v-model="ruleForm.verifyCode"
                  clearable
                  :placeholder="$t('VerifyCode')"
                  :prefix-icon="useRenderIcon('ri:shield-keyhole-line')"
                >
                  <template v-slot:append>
                    <ReImageVerify v-model:code="imgCode" />
                  </template>
                </el-input>
              </el-form-item>
            </Motion>

            <Motion :delay="230">
              <div class="flex justify-between w-full items-center mb-3">
                <div class="w-full text-left">
                  <el-button link type="primary" @click="goToRegister">
                    {{ $t("Register") }}
                  </el-button>
                </div>
                <div class="w-full text-end">
                  <el-button link type="primary" @click="goToForgotPassword">
                    {{ $t("Forgot password?") }}
                  </el-button>
                </div>
              </div>
            </Motion>

            <Motion :delay="250">
              <div class="flex justify-between mt-3 items-center">
                <el-button
                  class="w-full !uppercase"
                  size="large"
                  type="danger"
                  round
                  :loading="loading"
                  :disabled="disabled"
                  @click="onLogin(ruleFormRef)"
                >
                  <IconifyIconOnline
                    :icon="'ri:login-box-line'"
                    width="20"
                    class="mr-2"
                  />
                  {{ $t("Login") }}
                </el-button>
              </div>
            </Motion>

            <Motion :delay="350">
              <SocialLogin
                :loading="loading"
                :disabled="disabled"
                @social-login="handleSocialLogin"
              />
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
