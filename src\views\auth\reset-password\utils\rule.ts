import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";
import { useSettingStoreHook } from "@/store/modules/settings";

// Custom validators with proper parameter replacement
const otpValidator = (_rule: any, value: string, callback: Function) => {
  if (!value || value.trim() === "") {
    callback(new Error($t("OTP required")));
    return;
  }

  if (!/^\d+$/.test(value)) {
    callback(new Error($t("OTP must be numbers only")));
    return;
  }

  if (value.length !== 6) {
    callback(new Error($t("OTP must be {count} digits", { count: 6 })));
    return;
  }

  callback();
};

const passwordValidator = (_rule: any, value: string, callback: Function) => {
  if (!value || value.trim() === "") {
    callback(new Error($t("Password is required")));
    return;
  }

  // Get dynamic settings with proper structure
  const settingStore = useSettingStoreHook();
  const security = settingStore.settings?.security || {};
  const minLength = security.minPasswordLength || 6;
  const requireNumbers = security.requireNumbers || false;
  const requireSymbols = security.requireSymbols || false;
  const requireUppercase = security.requireUppercase || false;
  const requireLowercase = security.requireLowercase || false;

  // Check minimum length
  if (value.length < minLength) {
    callback(new Error($t("Password too short (min {count})", { count: minLength })));
    return;
  }

  // Check for numbers if required
  if (requireNumbers && !/\d/.test(value)) {
    callback(new Error($t("Password needs numbers")));
    return;
  }

  // Check for symbols if required
  if (requireSymbols && !/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
    callback(new Error($t("Password needs symbols")));
    return;
  }

  // Check for uppercase if required
  if (requireUppercase && !/[A-Z]/.test(value)) {
    callback(new Error($t("Password needs uppercase")));
    return;
  }

  // Check for lowercase if required
  if (requireLowercase && !/[a-z]/.test(value)) {
    callback(new Error($t("Password needs lowercase")));
    return;
  }

  callback();
};

const confirmPasswordValidator =
  (form: any) => (_rule: any, value: string, callback: Function) => {
    if (!value || value.trim() === "") {
      callback(new Error($t("Confirm password required")));
      return;
    }

    if (form.password && form.password !== value) {
      callback(new Error($t("Passwords don't match")));
      return;
    }

    callback();
  };

const resetPasswordRules = (form: any) => {
  return reactive<FormRules>({
    otp: [
      {
        validator: otpValidator,
        trigger: "blur"
      }
    ],
    password: [
      {
        validator: passwordValidator,
        trigger: "blur"
      }
    ],
    confirmPassword: [
      {
        validator: confirmPasswordValidator(form),
        trigger: "blur"
      }
    ]
  });
};

export { resetPasswordRules };
