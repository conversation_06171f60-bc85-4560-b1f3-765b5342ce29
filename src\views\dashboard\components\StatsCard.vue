<script setup lang="ts">
import { computed } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

interface Props {
  title: string;
  value: number | string;
  icon: string;
  color?: string;
  trend?: {
    value: number;
    isUp: boolean;
  };
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  color: "primary",
  loading: false
});

const cardClass = computed(() => {
  const colorMap = {
    primary: "border-blue-200 bg-blue-50",
    success: "border-green-200 bg-green-50",
    warning: "border-yellow-200 bg-yellow-50",
    danger: "border-red-200 bg-red-50",
    info: "border-gray-200 bg-gray-50"
  };
  return colorMap[props.color] || colorMap.primary;
});

const iconClass = computed(() => {
  const colorMap = {
    primary: "text-blue-600",
    success: "text-green-600",
    warning: "text-yellow-600",
    danger: "text-red-600",
    info: "text-gray-600"
  };
  return colorMap[props.color] || colorMap.primary;
});

const trendClass = computed(() => {
  if (!props.trend) return "";
  return props.trend.isUp ? "text-green-600" : "text-red-600";
});
</script>

<template>
  <el-card 
    :class="['stats-card', cardClass]"
    shadow="hover"
    :body-style="{ padding: '20px' }"
  >
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <div class="text-sm text-gray-600 mb-1">{{ title }}</div>
        <div class="text-2xl font-bold text-gray-900 mb-2">
          <el-skeleton-item 
            v-if="loading" 
            variant="text" 
            style="width: 60px; height: 32px;"
          />
          <span v-else>{{ value }}</span>
        </div>
        <div v-if="trend && !loading" :class="['text-sm flex items-center', trendClass]">
          <IconifyIconOffline 
            :icon="useRenderIcon(trend.isUp ? 'ep:arrow-up' : 'ep:arrow-down')"
            class="mr-1"
          />
          {{ Math.abs(trend.value) }}%
        </div>
      </div>
      <div :class="['text-3xl', iconClass]">
        <el-skeleton-item 
          v-if="loading" 
          variant="circle" 
          style="width: 48px; height: 48px;"
        />
        <IconifyIconOffline 
          v-else
          :icon="useRenderIcon(icon)"
        />
      </div>
    </div>
  </el-card>
</template>

<style scoped>
.stats-card {
  border-width: 1px;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
