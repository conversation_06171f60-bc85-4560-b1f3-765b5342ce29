import { defineStore } from "pinia";

import { useConvertKeyToCamel } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import { getChatBots } from "@/views/chat/utils/auth-api";
import type { ChatBot, Conversation } from "@/views/chat/utils/type";

export type ChatBotState = {
  loading: boolean;
  bots: ChatBot[];
  filters: Record<string, any>;
  selectedBot: ChatBot | null;
};

export const useChatBotStore = defineStore("chat-management", {
  state: (): ChatBotState => {
    return {
      loading: false,
      bots: [],
      filters: {
        searchQuery: ""
      },
      selectedBot: null
    };
  },

  actions: {
    // Fetch bots from API
    async getChatBots() {
      this.loading = true;
      try {
        const { data, success } = await getChatBots();
        if (success) {
          this.bots = useConvertKeyToCamel(data || []);
        }
      } catch (e: any) {
        console.info(e);
      } finally {
        this.loading = false;
      }
    },
    getSelectedChatBot(uuid: string) {
      if (!uuid) {
        this.selectedBot = null;
        return;
      }
      this.selectedBot = this.bots.find((bot: any) => bot.uuid == uuid) || null;
    },
    setConversation(conversation: any) {
      if (!this.selectedBot) {
        return;
      }
      if (
        !this.selectedBot.conversations ||
        !Array.isArray(this.selectedBot.conversations)
      ) {
        this.selectedBot.conversations = [];
      }
      this.selectedBot.conversations.push(conversation);
    }
  }
});

export function useChatBotStoreHook() {
  return useChatBotStore();
}
