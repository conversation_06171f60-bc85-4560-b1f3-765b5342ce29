import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/language/utils/type";

export const getSpaces = (params?: object) => {
  return http.request<Result>("get", "/api/auth/spaces", {
    params
  });
};

export const getSpaceById = (id: number) => {
  return http.request<Result>("get", `/api/auth/spaces/${id}`);
};

export const createSpace = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/spaces", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateSpaceByUuid = (uuid: string, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/spaces/${uuid}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const deleteSpaceById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/spaces/${id}`);
};

export const bulkDeleteSpaces = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/spaces/bulk-delete", {
    data
  });
};

export const destroySpaceByUuid = (uuid: string) => {
  return http.request<Result>("delete", `/api/auth/spaces/${uuid}`);
};

export const bulkDestroySpaces = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/spaces/bulk-destroy", {
    data
  });
};

export const restoreSpaceById = (id: number) => {
  return http.request<Result>("put", `/api/auth/spaces/${id}/restore`);
};

export const bulkRestoreSpaces = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/spaces/bulk-restore", {
    data
  });
};

export const dropdownSpaces = () => {
  return http.request<Result>("get", "/api/auth/spaces/dropdown");
};



export const getBots = (params?: object) => {
  return http.request<Result>("get", "/api/auth/bots", {
    params
  });
};

export const getBotById = (id: any) => {
  return http.request<Result>("get", `/api/auth/bots/${id}`);
};

export const createBot = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/bots", {
    data: useConvertKeyToSnake(data),
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

export const updateBotById = (id: any, data: FormItemProps) => {
  return http.request<Result>("post", `/api/auth/bots/${id}`, {
    data: useConvertKeyToSnake(data),
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

export const deleteBotById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/bots/${id}`);
};

export const bulkDeleteBots = (ids: number[]) => {
  return http.request<Result>("delete", "/api/auth/bots/bulk", {
    data: { ids }
  });
};

export const destroyBotById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/bots/${id}/force`);
};

export const bulkDestroyBots = (ids: number[]) => {
  return http.request<Result>("delete", "/api/auth/bots/bulk/force", {
    data: { ids }
  });
};

export const restoreBotById = (id: number) => {
  return http.request<Result>("post", `/api/auth/bots/${id}/restore`);
};

export const bulkRestoreBots = (ids: number[]) => {
  return http.request<Result>("post", "/api/auth/bots/bulk/restore", {
    data: { ids }
  });
};

// Get AI Models for dropdown
export const getAiModels = () => {
  return http.request<Result>("get", "/api/auth/model-ai");
};

export const getAiModelDropdown = () => {
  return http.request<Result>("get", `/api/model-ai/dropdown`);
};

export const getGeneralPrompts = (obj: object) => {
  return http.request<Result>("get", `/api/auth/bot-general-prompt`, {
    params: obj
  });
};

